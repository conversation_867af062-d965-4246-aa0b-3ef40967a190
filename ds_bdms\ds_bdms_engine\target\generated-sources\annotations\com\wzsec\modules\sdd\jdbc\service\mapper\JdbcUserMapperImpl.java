package com.wzsec.modules.sdd.jdbc.service.mapper;

import com.wzsec.modules.sdd.jdbc.domain.JdbcDb;
import com.wzsec.modules.sdd.jdbc.domain.JdbcUser;
import com.wzsec.modules.sdd.jdbc.service.dto.JdbcDbSmallDto;
import com.wzsec.modules.sdd.jdbc.service.dto.JdbcUserDto;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T18:00:03+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class JdbcUserMapperImpl implements JdbcUserMapper {

    @Autowired
    private JdbcDbMapper jdbcDbMapper;

    @Override
    public JdbcUser toEntity(JdbcUserDto dto) {
        if ( dto == null ) {
            return null;
        }

        JdbcUser jdbcUser = new JdbcUser();

        jdbcUser.setId( dto.getId() );
        jdbcUser.setUsername( dto.getUsername() );
        jdbcUser.setPassword( dto.getPassword() );
        jdbcUser.setNickName( dto.getNickName() );
        jdbcUser.setSex( dto.getSex() );
        jdbcUser.setEmail( dto.getEmail() );
        jdbcUser.setPhone( dto.getPhone() );
        jdbcUser.setRole( dto.getRole() );
        jdbcUser.setEnabled( dto.getEnabled() );
        jdbcUser.setCreatetime( dto.getCreatetime() );
        jdbcUser.setLastLoginTime( dto.getLastLoginTime() );
        jdbcUser.setLastPasswordResetTime( dto.getLastPasswordResetTime() );
        jdbcUser.setSparefield1( dto.getSparefield1() );
        jdbcUser.setSparefield2( dto.getSparefield2() );
        jdbcUser.setSparefield3( dto.getSparefield3() );
        jdbcUser.setSparefield4( dto.getSparefield4() );
        jdbcUser.setSparefield5( dto.getSparefield5() );
        jdbcUser.setJdbcDbs( jdbcDbSmallDtoSetToJdbcDbSet( dto.getJdbcDbs() ) );

        return jdbcUser;
    }

    @Override
    public JdbcUserDto toDto(JdbcUser entity) {
        if ( entity == null ) {
            return null;
        }

        JdbcUserDto jdbcUserDto = new JdbcUserDto();

        jdbcUserDto.setId( entity.getId() );
        jdbcUserDto.setUsername( entity.getUsername() );
        jdbcUserDto.setPassword( entity.getPassword() );
        jdbcUserDto.setNickName( entity.getNickName() );
        jdbcUserDto.setSex( entity.getSex() );
        jdbcUserDto.setEmail( entity.getEmail() );
        jdbcUserDto.setPhone( entity.getPhone() );
        jdbcUserDto.setRole( entity.getRole() );
        jdbcUserDto.setEnabled( entity.getEnabled() );
        jdbcUserDto.setCreatetime( entity.getCreatetime() );
        jdbcUserDto.setLastLoginTime( entity.getLastLoginTime() );
        jdbcUserDto.setLastPasswordResetTime( entity.getLastPasswordResetTime() );
        jdbcUserDto.setSparefield1( entity.getSparefield1() );
        jdbcUserDto.setSparefield2( entity.getSparefield2() );
        jdbcUserDto.setSparefield3( entity.getSparefield3() );
        jdbcUserDto.setSparefield4( entity.getSparefield4() );
        jdbcUserDto.setSparefield5( entity.getSparefield5() );
        jdbcUserDto.setJdbcDbs( jdbcDbSetToJdbcDbSmallDtoSet( entity.getJdbcDbs() ) );

        return jdbcUserDto;
    }

    @Override
    public List<JdbcUser> toEntity(List<JdbcUserDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<JdbcUser> list = new ArrayList<JdbcUser>( dtoList.size() );
        for ( JdbcUserDto jdbcUserDto : dtoList ) {
            list.add( toEntity( jdbcUserDto ) );
        }

        return list;
    }

    @Override
    public List<JdbcUserDto> toDto(List<JdbcUser> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<JdbcUserDto> list = new ArrayList<JdbcUserDto>( entityList.size() );
        for ( JdbcUser jdbcUser : entityList ) {
            list.add( toDto( jdbcUser ) );
        }

        return list;
    }

    protected JdbcDb jdbcDbSmallDtoToJdbcDb(JdbcDbSmallDto jdbcDbSmallDto) {
        if ( jdbcDbSmallDto == null ) {
            return null;
        }

        JdbcDb jdbcDb = new JdbcDb();

        jdbcDb.setId( jdbcDbSmallDto.getId() );
        jdbcDb.setLogicdbename( jdbcDbSmallDto.getLogicdbename() );
        jdbcDb.setLogicdbcname( jdbcDbSmallDto.getLogicdbcname() );

        return jdbcDb;
    }

    protected Set<JdbcDb> jdbcDbSmallDtoSetToJdbcDbSet(Set<JdbcDbSmallDto> set) {
        if ( set == null ) {
            return null;
        }

        Set<JdbcDb> set1 = new HashSet<JdbcDb>( Math.max( (int) ( set.size() / .75f ) + 1, 16 ) );
        for ( JdbcDbSmallDto jdbcDbSmallDto : set ) {
            set1.add( jdbcDbSmallDtoToJdbcDb( jdbcDbSmallDto ) );
        }

        return set1;
    }

    protected JdbcDbSmallDto jdbcDbToJdbcDbSmallDto(JdbcDb jdbcDb) {
        if ( jdbcDb == null ) {
            return null;
        }

        JdbcDbSmallDto jdbcDbSmallDto = new JdbcDbSmallDto();

        jdbcDbSmallDto.setId( jdbcDb.getId() );
        jdbcDbSmallDto.setLogicdbename( jdbcDb.getLogicdbename() );
        jdbcDbSmallDto.setLogicdbcname( jdbcDb.getLogicdbcname() );

        return jdbcDbSmallDto;
    }

    protected Set<JdbcDbSmallDto> jdbcDbSetToJdbcDbSmallDtoSet(Set<JdbcDb> set) {
        if ( set == null ) {
            return null;
        }

        Set<JdbcDbSmallDto> set1 = new HashSet<JdbcDbSmallDto>( Math.max( (int) ( set.size() / .75f ) + 1, 16 ) );
        for ( JdbcDb jdbcDb : set ) {
            set1.add( jdbcDbToJdbcDbSmallDto( jdbcDb ) );
        }

        return set1;
    }
}
