package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.MaskPictaskconfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2022-04-18
 */
public interface MaskPictaskconfigRepository extends JpaRepository<MaskPictaskconfig, Integer>, JpaSpecificationExecutor<MaskPictaskconfig> {
    @Query(value = "select MAX(`taskname`) from sdd_mask_pictaskconfig where taskname like concat('%',?1,'%')", nativeQuery = true)
    String findMAXTasknoByPrefix(String prefix);

    /**
     * 根据ID修改状态
     *
     * @param id
     */
    @Modifying
    @Transactional
    @Query(value = "UPDATE sdd_mask_pictaskconfig SET executionstate = ?2 WHERE id=?1 ", nativeQuery = true)
    void updateTaskStatus(Integer id, String status);
}