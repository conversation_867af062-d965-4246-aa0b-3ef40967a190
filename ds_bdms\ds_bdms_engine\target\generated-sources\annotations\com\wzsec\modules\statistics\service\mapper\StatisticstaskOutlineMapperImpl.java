package com.wzsec.modules.statistics.service.mapper;

import com.wzsec.modules.statistics.domain.StatisticstaskOutline;
import com.wzsec.modules.statistics.service.dto.StatisticstaskOutlineDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T18:00:04+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class StatisticstaskOutlineMapperImpl implements StatisticstaskOutlineMapper {

    @Override
    public StatisticstaskOutline toEntity(StatisticstaskOutlineDto dto) {
        if ( dto == null ) {
            return null;
        }

        StatisticstaskOutline statisticstaskOutline = new StatisticstaskOutline();

        statisticstaskOutline.setId( dto.getId() );
        statisticstaskOutline.setTaskname( dto.getTaskname() );
        statisticstaskOutline.setSrcname( dto.getSrcname() );
        statisticstaskOutline.setDbname( dto.getDbname() );
        statisticstaskOutline.setTablecount( dto.getTablecount() );
        statisticstaskOutline.setCreateuser( dto.getCreateuser() );
        statisticstaskOutline.setCreatetime( dto.getCreatetime() );
        statisticstaskOutline.setSparefield1( dto.getSparefield1() );
        statisticstaskOutline.setSparefield2( dto.getSparefield2() );
        statisticstaskOutline.setSparefield3( dto.getSparefield3() );
        statisticstaskOutline.setSparefield4( dto.getSparefield4() );

        return statisticstaskOutline;
    }

    @Override
    public StatisticstaskOutlineDto toDto(StatisticstaskOutline entity) {
        if ( entity == null ) {
            return null;
        }

        StatisticstaskOutlineDto statisticstaskOutlineDto = new StatisticstaskOutlineDto();

        statisticstaskOutlineDto.setId( entity.getId() );
        statisticstaskOutlineDto.setTaskname( entity.getTaskname() );
        statisticstaskOutlineDto.setSrcname( entity.getSrcname() );
        statisticstaskOutlineDto.setDbname( entity.getDbname() );
        statisticstaskOutlineDto.setTablecount( entity.getTablecount() );
        statisticstaskOutlineDto.setCreateuser( entity.getCreateuser() );
        statisticstaskOutlineDto.setCreatetime( entity.getCreatetime() );
        statisticstaskOutlineDto.setSparefield1( entity.getSparefield1() );
        statisticstaskOutlineDto.setSparefield2( entity.getSparefield2() );
        statisticstaskOutlineDto.setSparefield3( entity.getSparefield3() );
        statisticstaskOutlineDto.setSparefield4( entity.getSparefield4() );

        return statisticstaskOutlineDto;
    }

    @Override
    public List<StatisticstaskOutline> toEntity(List<StatisticstaskOutlineDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<StatisticstaskOutline> list = new ArrayList<StatisticstaskOutline>( dtoList.size() );
        for ( StatisticstaskOutlineDto statisticstaskOutlineDto : dtoList ) {
            list.add( toEntity( statisticstaskOutlineDto ) );
        }

        return list;
    }

    @Override
    public List<StatisticstaskOutlineDto> toDto(List<StatisticstaskOutline> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<StatisticstaskOutlineDto> list = new ArrayList<StatisticstaskOutlineDto>( entityList.size() );
        for ( StatisticstaskOutline statisticstaskOutline : entityList ) {
            list.add( toDto( statisticstaskOutline ) );
        }

        return list;
    }
}
