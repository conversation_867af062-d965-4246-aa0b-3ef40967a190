package com.wzsec.modules.sdd.basefield.service.mapper;

import com.wzsec.modules.sdd.basefield.domain.Basefield;
import com.wzsec.modules.sdd.basefield.service.dto.BasefieldDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T18:00:03+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class BasefieldMapperImpl implements BasefieldMapper {

    @Override
    public Basefield toEntity(BasefieldDto dto) {
        if ( dto == null ) {
            return null;
        }

        Basefield basefield = new Basefield();

        basefield.setId( dto.getId() );
        basefield.setFieldid( dto.getFieldid() );
        basefield.setFieldename( dto.getFieldename() );
        basefield.setFieldcname( dto.getFieldcname() );
        basefield.setNameexplain( dto.getNameexplain() );
        basefield.setChineseotherexplain( dto.getChineseotherexplain() );
        basefield.setEnglishotherexplain( dto.getEnglishotherexplain() );
        basefield.setSenLevel( dto.getSenLevel() );
        basefield.setCategory( dto.getCategory() );
        basefield.setSource( dto.getSource() );
        basefield.setAlgorithmid( dto.getAlgorithmid() );
        basefield.setIsusable( dto.getIsusable() );
        basefield.setTabid( dto.getTabid() );
        basefield.setTabename( dto.getTabename() );
        basefield.setTabcname( dto.getTabcname() );
        basefield.setTabdbname( dto.getTabdbname() );
        basefield.setSparefield1( dto.getSparefield1() );
        basefield.setSparefield2( dto.getSparefield2() );
        basefield.setSparefield3( dto.getSparefield3() );
        basefield.setSparefield4( dto.getSparefield4() );
        basefield.setInserttime( dto.getInserttime() );

        return basefield;
    }

    @Override
    public BasefieldDto toDto(Basefield entity) {
        if ( entity == null ) {
            return null;
        }

        BasefieldDto basefieldDto = new BasefieldDto();

        basefieldDto.setId( entity.getId() );
        basefieldDto.setFieldid( entity.getFieldid() );
        basefieldDto.setFieldename( entity.getFieldename() );
        basefieldDto.setFieldcname( entity.getFieldcname() );
        basefieldDto.setNameexplain( entity.getNameexplain() );
        basefieldDto.setChineseotherexplain( entity.getChineseotherexplain() );
        basefieldDto.setEnglishotherexplain( entity.getEnglishotherexplain() );
        basefieldDto.setSenLevel( entity.getSenLevel() );
        basefieldDto.setCategory( entity.getCategory() );
        basefieldDto.setSource( entity.getSource() );
        basefieldDto.setAlgorithmid( entity.getAlgorithmid() );
        basefieldDto.setIsusable( entity.getIsusable() );
        basefieldDto.setTabid( entity.getTabid() );
        basefieldDto.setTabename( entity.getTabename() );
        basefieldDto.setTabcname( entity.getTabcname() );
        basefieldDto.setTabdbname( entity.getTabdbname() );
        basefieldDto.setSparefield1( entity.getSparefield1() );
        basefieldDto.setSparefield2( entity.getSparefield2() );
        basefieldDto.setSparefield3( entity.getSparefield3() );
        basefieldDto.setSparefield4( entity.getSparefield4() );
        basefieldDto.setInserttime( entity.getInserttime() );

        return basefieldDto;
    }

    @Override
    public List<Basefield> toEntity(List<BasefieldDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Basefield> list = new ArrayList<Basefield>( dtoList.size() );
        for ( BasefieldDto basefieldDto : dtoList ) {
            list.add( toEntity( basefieldDto ) );
        }

        return list;
    }

    @Override
    public List<BasefieldDto> toDto(List<Basefield> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<BasefieldDto> list = new ArrayList<BasefieldDto>( entityList.size() );
        for ( Basefield basefield : entityList ) {
            list.add( toDto( basefield ) );
        }

        return list;
    }
}
