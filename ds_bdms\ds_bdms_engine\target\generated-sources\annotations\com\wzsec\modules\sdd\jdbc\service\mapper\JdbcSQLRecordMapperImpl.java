package com.wzsec.modules.sdd.jdbc.service.mapper;

import com.wzsec.modules.sdd.jdbc.domain.JdbcSQLRecord;
import com.wzsec.modules.sdd.jdbc.service.dto.JdbcSQLRecordDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T18:00:03+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class JdbcSQLRecordMapperImpl implements JdbcSQLRecordMapper {

    @Override
    public JdbcSQLRecord toEntity(JdbcSQLRecordDto dto) {
        if ( dto == null ) {
            return null;
        }

        JdbcSQLRecord jdbcSQLRecord = new JdbcSQLRecord();

        jdbcSQLRecord.setRequesttime( dto.getRequesttime() );
        jdbcSQLRecord.setRequestip( dto.getRequestip() );
        jdbcSQLRecord.setRequestuser( dto.getRequestuser() );
        jdbcSQLRecord.setRequestsql( dto.getRequestsql() );
        jdbcSQLRecord.setRequestparam( dto.getRequestparam() );
        jdbcSQLRecord.setRewritesql( dto.getRewritesql() );
        jdbcSQLRecord.setRewriteparam( dto.getRewriteparam() );
        jdbcSQLRecord.setSqltype( dto.getSqltype() );
        jdbcSQLRecord.setRewritestate( dto.getRewritestate() );
        jdbcSQLRecord.setHandlestate( dto.getHandlestate() );
        jdbcSQLRecord.setSourceurl( dto.getSourceurl() );
        jdbcSQLRecord.setDbname( dto.getDbname() );
        jdbcSQLRecord.setExecstate( dto.getExecstate() );
        jdbcSQLRecord.setResponserownum( dto.getResponserownum() );
        jdbcSQLRecord.setInserttime( dto.getInserttime() );
        jdbcSQLRecord.setSparefield1( dto.getSparefield1() );
        jdbcSQLRecord.setSparefield2( dto.getSparefield2() );
        jdbcSQLRecord.setSparefield3( dto.getSparefield3() );
        jdbcSQLRecord.setSparefield4( dto.getSparefield4() );
        jdbcSQLRecord.setSparefield5( dto.getSparefield5() );

        return jdbcSQLRecord;
    }

    @Override
    public JdbcSQLRecordDto toDto(JdbcSQLRecord entity) {
        if ( entity == null ) {
            return null;
        }

        JdbcSQLRecordDto jdbcSQLRecordDto = new JdbcSQLRecordDto();

        jdbcSQLRecordDto.setRequesttime( entity.getRequesttime() );
        jdbcSQLRecordDto.setRequestip( entity.getRequestip() );
        jdbcSQLRecordDto.setRequestuser( entity.getRequestuser() );
        jdbcSQLRecordDto.setRequestsql( entity.getRequestsql() );
        jdbcSQLRecordDto.setRequestparam( entity.getRequestparam() );
        jdbcSQLRecordDto.setRewritesql( entity.getRewritesql() );
        jdbcSQLRecordDto.setRewriteparam( entity.getRewriteparam() );
        jdbcSQLRecordDto.setSqltype( entity.getSqltype() );
        jdbcSQLRecordDto.setRewritestate( entity.getRewritestate() );
        jdbcSQLRecordDto.setHandlestate( entity.getHandlestate() );
        jdbcSQLRecordDto.setSourceurl( entity.getSourceurl() );
        jdbcSQLRecordDto.setDbname( entity.getDbname() );
        jdbcSQLRecordDto.setExecstate( entity.getExecstate() );
        jdbcSQLRecordDto.setResponserownum( entity.getResponserownum() );
        jdbcSQLRecordDto.setInserttime( entity.getInserttime() );
        jdbcSQLRecordDto.setSparefield1( entity.getSparefield1() );
        jdbcSQLRecordDto.setSparefield2( entity.getSparefield2() );
        jdbcSQLRecordDto.setSparefield3( entity.getSparefield3() );
        jdbcSQLRecordDto.setSparefield4( entity.getSparefield4() );
        jdbcSQLRecordDto.setSparefield5( entity.getSparefield5() );

        return jdbcSQLRecordDto;
    }

    @Override
    public List<JdbcSQLRecord> toEntity(List<JdbcSQLRecordDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<JdbcSQLRecord> list = new ArrayList<JdbcSQLRecord>( dtoList.size() );
        for ( JdbcSQLRecordDto jdbcSQLRecordDto : dtoList ) {
            list.add( toEntity( jdbcSQLRecordDto ) );
        }

        return list;
    }

    @Override
    public List<JdbcSQLRecordDto> toDto(List<JdbcSQLRecord> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<JdbcSQLRecordDto> list = new ArrayList<JdbcSQLRecordDto>( entityList.size() );
        for ( JdbcSQLRecord jdbcSQLRecord : entityList ) {
            list.add( toDto( jdbcSQLRecord ) );
        }

        return list;
    }
}
