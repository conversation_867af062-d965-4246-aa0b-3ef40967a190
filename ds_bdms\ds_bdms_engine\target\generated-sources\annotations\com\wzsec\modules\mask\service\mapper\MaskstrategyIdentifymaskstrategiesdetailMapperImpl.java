package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskstrategyIdentifymaskstrategiesdetail;
import com.wzsec.modules.mask.service.dto.MaskstrategyIdentifymaskstrategiesdetailDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T18:00:04+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskstrategyIdentifymaskstrategiesdetailMapperImpl implements MaskstrategyIdentifymaskstrategiesdetailMapper {

    @Override
    public MaskstrategyIdentifymaskstrategiesdetail toEntity(MaskstrategyIdentifymaskstrategiesdetailDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskstrategyIdentifymaskstrategiesdetail maskstrategyIdentifymaskstrategiesdetail = new MaskstrategyIdentifymaskstrategiesdetail();

        maskstrategyIdentifymaskstrategiesdetail.setId( dto.getId() );
        maskstrategyIdentifymaskstrategiesdetail.setStrategyid( dto.getStrategyid() );
        maskstrategyIdentifymaskstrategiesdetail.setDataname( dto.getDataname() );
        maskstrategyIdentifymaskstrategiesdetail.setSenruleid( dto.getSenruleid() );
        maskstrategyIdentifymaskstrategiesdetail.setMaskruleid( dto.getMaskruleid() );
        maskstrategyIdentifymaskstrategiesdetail.setAlgorithmid( dto.getAlgorithmid() );
        maskstrategyIdentifymaskstrategiesdetail.setParam( dto.getParam() );
        maskstrategyIdentifymaskstrategiesdetail.setSecretkey( dto.getSecretkey() );
        maskstrategyIdentifymaskstrategiesdetail.setSparefield1( dto.getSparefield1() );
        maskstrategyIdentifymaskstrategiesdetail.setSparefield2( dto.getSparefield2() );
        maskstrategyIdentifymaskstrategiesdetail.setSparefield3( dto.getSparefield3() );
        maskstrategyIdentifymaskstrategiesdetail.setSparefield4( dto.getSparefield4() );
        maskstrategyIdentifymaskstrategiesdetail.setSparefield5( dto.getSparefield5() );

        return maskstrategyIdentifymaskstrategiesdetail;
    }

    @Override
    public MaskstrategyIdentifymaskstrategiesdetailDto toDto(MaskstrategyIdentifymaskstrategiesdetail entity) {
        if ( entity == null ) {
            return null;
        }

        MaskstrategyIdentifymaskstrategiesdetailDto maskstrategyIdentifymaskstrategiesdetailDto = new MaskstrategyIdentifymaskstrategiesdetailDto();

        maskstrategyIdentifymaskstrategiesdetailDto.setId( entity.getId() );
        maskstrategyIdentifymaskstrategiesdetailDto.setStrategyid( entity.getStrategyid() );
        maskstrategyIdentifymaskstrategiesdetailDto.setDataname( entity.getDataname() );
        maskstrategyIdentifymaskstrategiesdetailDto.setSenruleid( entity.getSenruleid() );
        maskstrategyIdentifymaskstrategiesdetailDto.setMaskruleid( entity.getMaskruleid() );
        maskstrategyIdentifymaskstrategiesdetailDto.setAlgorithmid( entity.getAlgorithmid() );
        maskstrategyIdentifymaskstrategiesdetailDto.setParam( entity.getParam() );
        maskstrategyIdentifymaskstrategiesdetailDto.setSecretkey( entity.getSecretkey() );
        maskstrategyIdentifymaskstrategiesdetailDto.setSparefield1( entity.getSparefield1() );
        maskstrategyIdentifymaskstrategiesdetailDto.setSparefield2( entity.getSparefield2() );
        maskstrategyIdentifymaskstrategiesdetailDto.setSparefield3( entity.getSparefield3() );
        maskstrategyIdentifymaskstrategiesdetailDto.setSparefield4( entity.getSparefield4() );
        maskstrategyIdentifymaskstrategiesdetailDto.setSparefield5( entity.getSparefield5() );

        return maskstrategyIdentifymaskstrategiesdetailDto;
    }

    @Override
    public List<MaskstrategyIdentifymaskstrategiesdetail> toEntity(List<MaskstrategyIdentifymaskstrategiesdetailDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskstrategyIdentifymaskstrategiesdetail> list = new ArrayList<MaskstrategyIdentifymaskstrategiesdetail>( dtoList.size() );
        for ( MaskstrategyIdentifymaskstrategiesdetailDto maskstrategyIdentifymaskstrategiesdetailDto : dtoList ) {
            list.add( toEntity( maskstrategyIdentifymaskstrategiesdetailDto ) );
        }

        return list;
    }

    @Override
    public List<MaskstrategyIdentifymaskstrategiesdetailDto> toDto(List<MaskstrategyIdentifymaskstrategiesdetail> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskstrategyIdentifymaskstrategiesdetailDto> list = new ArrayList<MaskstrategyIdentifymaskstrategiesdetailDto>( entityList.size() );
        for ( MaskstrategyIdentifymaskstrategiesdetail maskstrategyIdentifymaskstrategiesdetail : entityList ) {
            list.add( toDto( maskstrategyIdentifymaskstrategiesdetail ) );
        }

        return list;
    }
}
