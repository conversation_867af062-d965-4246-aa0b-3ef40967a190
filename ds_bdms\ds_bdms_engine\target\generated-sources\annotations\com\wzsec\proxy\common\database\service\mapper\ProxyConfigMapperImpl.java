package com.wzsec.proxy.common.database.service.mapper;

import com.wzsec.proxy.common.database.bean.ProxyConfig;
import com.wzsec.proxy.common.database.service.dto.ProxyConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T18:00:04+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ProxyConfigMapperImpl implements ProxyConfigMapper {

    @Override
    public ProxyConfig toEntity(ProxyConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        ProxyConfig proxyConfig = new ProxyConfig();

        proxyConfig.setId( dto.getId() );
        if ( dto.getSrcid() != null ) {
            proxyConfig.setSrcid( dto.getSrcid() );
        }
        proxyConfig.setExecutionengine( dto.getExecutionengine() );
        proxyConfig.setProxyport( dto.getProxyport() );
        proxyConfig.setIsvalid( dto.getIsvalid() );
        proxyConfig.setNote( dto.getNote() );
        proxyConfig.setCreateuser( dto.getCreateuser() );
        proxyConfig.setCreatetime( dto.getCreatetime() );
        proxyConfig.setUpdateuser( dto.getUpdateuser() );
        proxyConfig.setUpdatetime( dto.getUpdatetime() );
        proxyConfig.setSparefield1( dto.getSparefield1() );
        proxyConfig.setSparefield2( dto.getSparefield2() );
        proxyConfig.setSparefield3( dto.getSparefield3() );
        proxyConfig.setSparefield4( dto.getSparefield4() );
        proxyConfig.setSparefield5( dto.getSparefield5() );

        return proxyConfig;
    }

    @Override
    public ProxyConfigDto toDto(ProxyConfig entity) {
        if ( entity == null ) {
            return null;
        }

        ProxyConfigDto proxyConfigDto = new ProxyConfigDto();

        proxyConfigDto.setId( entity.getId() );
        proxyConfigDto.setSrcid( entity.getSrcid() );
        proxyConfigDto.setExecutionengine( entity.getExecutionengine() );
        proxyConfigDto.setProxyport( entity.getProxyport() );
        proxyConfigDto.setIsvalid( entity.getIsvalid() );
        proxyConfigDto.setNote( entity.getNote() );
        proxyConfigDto.setCreateuser( entity.getCreateuser() );
        proxyConfigDto.setCreatetime( entity.getCreatetime() );
        proxyConfigDto.setUpdateuser( entity.getUpdateuser() );
        proxyConfigDto.setUpdatetime( entity.getUpdatetime() );
        proxyConfigDto.setSparefield1( entity.getSparefield1() );
        proxyConfigDto.setSparefield2( entity.getSparefield2() );
        proxyConfigDto.setSparefield3( entity.getSparefield3() );
        proxyConfigDto.setSparefield4( entity.getSparefield4() );
        proxyConfigDto.setSparefield5( entity.getSparefield5() );

        return proxyConfigDto;
    }

    @Override
    public List<ProxyConfig> toEntity(List<ProxyConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ProxyConfig> list = new ArrayList<ProxyConfig>( dtoList.size() );
        for ( ProxyConfigDto proxyConfigDto : dtoList ) {
            list.add( toEntity( proxyConfigDto ) );
        }

        return list;
    }

    @Override
    public List<ProxyConfigDto> toDto(List<ProxyConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ProxyConfigDto> list = new ArrayList<ProxyConfigDto>( entityList.size() );
        for ( ProxyConfig proxyConfig : entityList ) {
            list.add( toDto( proxyConfig ) );
        }

        return list;
    }
}
