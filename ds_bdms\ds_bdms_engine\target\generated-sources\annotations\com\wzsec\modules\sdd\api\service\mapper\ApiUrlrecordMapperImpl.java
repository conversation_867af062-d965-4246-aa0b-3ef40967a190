package com.wzsec.modules.sdd.api.service.mapper;

import com.wzsec.modules.sdd.api.domain.ApiUrlrecord;
import com.wzsec.modules.sdd.api.service.dto.ApiUrlrecordDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T18:00:04+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiUrlrecordMapperImpl implements ApiUrlrecordMapper {

    @Override
    public ApiUrlrecord toEntity(ApiUrlrecordDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiUrlrecord apiUrlrecord = new ApiUrlrecord();

        apiUrlrecord.setId( dto.getId() );
        apiUrlrecord.setRequesttime( dto.getRequesttime() );
        apiUrlrecord.setRequesprotocol( dto.getRequesprotocol() );
        apiUrlrecord.setRequestmethond( dto.getRequestmethond() );
        apiUrlrecord.setRequestip( dto.getRequestip() );
        apiUrlrecord.setRequesturl( dto.getRequesturl() );
        apiUrlrecord.setRequestparam( dto.getRequestparam() );
        apiUrlrecord.setServerhost( dto.getServerhost() );
        apiUrlrecord.setServerport( dto.getServerport() );
        apiUrlrecord.setServerapi( dto.getServerapi() );
        apiUrlrecord.setServerurl( dto.getServerurl() );
        apiUrlrecord.setResponseparam( dto.getResponseparam() );
        apiUrlrecord.setResponsedatalength( dto.getResponsedatalength() );
        apiUrlrecord.setMaskparam( dto.getMaskparam() );
        apiUrlrecord.setMaskdatalength( dto.getMaskdatalength() );
        apiUrlrecord.setIsmask( dto.getIsmask() );
        apiUrlrecord.setMaskstatus( dto.getMaskstatus() );
        apiUrlrecord.setHandletime( dto.getHandletime() );
        apiUrlrecord.setInserttime( dto.getInserttime() );
        apiUrlrecord.setSparefield1( dto.getSparefield1() );
        apiUrlrecord.setSparefield2( dto.getSparefield2() );
        apiUrlrecord.setSparefield3( dto.getSparefield3() );
        apiUrlrecord.setSparefield4( dto.getSparefield4() );
        apiUrlrecord.setSparefield5( dto.getSparefield5() );

        return apiUrlrecord;
    }

    @Override
    public ApiUrlrecordDto toDto(ApiUrlrecord entity) {
        if ( entity == null ) {
            return null;
        }

        ApiUrlrecordDto apiUrlrecordDto = new ApiUrlrecordDto();

        apiUrlrecordDto.setId( entity.getId() );
        apiUrlrecordDto.setRequesttime( entity.getRequesttime() );
        apiUrlrecordDto.setRequesprotocol( entity.getRequesprotocol() );
        apiUrlrecordDto.setRequestmethond( entity.getRequestmethond() );
        apiUrlrecordDto.setRequestip( entity.getRequestip() );
        apiUrlrecordDto.setRequesturl( entity.getRequesturl() );
        apiUrlrecordDto.setRequestparam( entity.getRequestparam() );
        apiUrlrecordDto.setServerhost( entity.getServerhost() );
        apiUrlrecordDto.setServerport( entity.getServerport() );
        apiUrlrecordDto.setServerapi( entity.getServerapi() );
        apiUrlrecordDto.setServerurl( entity.getServerurl() );
        apiUrlrecordDto.setResponseparam( entity.getResponseparam() );
        apiUrlrecordDto.setResponsedatalength( entity.getResponsedatalength() );
        apiUrlrecordDto.setMaskparam( entity.getMaskparam() );
        apiUrlrecordDto.setMaskdatalength( entity.getMaskdatalength() );
        apiUrlrecordDto.setIsmask( entity.getIsmask() );
        apiUrlrecordDto.setMaskstatus( entity.getMaskstatus() );
        apiUrlrecordDto.setHandletime( entity.getHandletime() );
        apiUrlrecordDto.setInserttime( entity.getInserttime() );
        apiUrlrecordDto.setSparefield1( entity.getSparefield1() );
        apiUrlrecordDto.setSparefield2( entity.getSparefield2() );
        apiUrlrecordDto.setSparefield3( entity.getSparefield3() );
        apiUrlrecordDto.setSparefield4( entity.getSparefield4() );
        apiUrlrecordDto.setSparefield5( entity.getSparefield5() );

        return apiUrlrecordDto;
    }

    @Override
    public List<ApiUrlrecord> toEntity(List<ApiUrlrecordDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiUrlrecord> list = new ArrayList<ApiUrlrecord>( dtoList.size() );
        for ( ApiUrlrecordDto apiUrlrecordDto : dtoList ) {
            list.add( toEntity( apiUrlrecordDto ) );
        }

        return list;
    }

    @Override
    public List<ApiUrlrecordDto> toDto(List<ApiUrlrecord> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiUrlrecordDto> list = new ArrayList<ApiUrlrecordDto>( entityList.size() );
        for ( ApiUrlrecord apiUrlrecord : entityList ) {
            list.add( toDto( apiUrlrecord ) );
        }

        return list;
    }
}
