<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="cb264ad8-4cc2-47ef-8691-64a929f29e97" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/quartz/config/MaskPicConfigJob.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/quartz/config/MaskPicScanConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/quartz/config/MaskVideoConfigJob.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/quartz/config/MaskVideoScanConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/dotask/mask/rest/PictureTaskController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/dotask/mask/rest/PictureTaskController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/dotask/mask/rest/VideoTaskController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/dotask/mask/rest/VideoTaskController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/dotask/mask/service/impl/DoPictureTaskServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/dotask/mask/service/impl/DoPictureTaskServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/dotask/mask/service/impl/DoVideoTaskServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/dotask/mask/service/impl/DoVideoTaskServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/modules/mask/repository/DBTaskConfigRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/modules/mask/repository/DBTaskConfigRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/modules/mask/repository/MaskPictaskconfigRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/modules/mask/repository/MaskPictaskconfigRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/modules/mask/repository/MaskVideotaskconfigRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/modules/mask/repository/MaskVideotaskconfigRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/modules/mask/service/MaskPictaskconfigService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/modules/mask/service/MaskPictaskconfigService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/modules/mask/service/MaskVideotaskconfigService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/modules/mask/service/MaskVideotaskconfigService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/modules/mask/service/impl/DBTaskConfigServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/modules/mask/service/impl/DBTaskConfigServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/modules/mask/service/impl/MaskPictaskconfigServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/modules/mask/service/impl/MaskPictaskconfigServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/modules/mask/service/impl/MaskVideotaskconfigServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/modules/mask/service/impl/MaskVideotaskconfigServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/modules/security/config/SecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/modules/security/config/SecurityConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_engine/src/main/resources/config/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_engine/src/main/resources/config/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_logging/src/main/java/com/wzsec/service/impl/LogServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_logging/src/main/java/com/wzsec/service/impl/LogServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_logging/src/main/java/com/wzsec/service/mapper/LogAlarmSettingsMapper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_logging/src/main/java/com/wzsec/service/mapper/LogErrorMapper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_logging/src/main/java/com/wzsec/service/mapper/LogSmallMapper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/domain/MaskPictaskconfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/domain/MaskPictaskconfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/domain/MaskVideotaskconfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/domain/MaskVideotaskconfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/rest/MaskPictaskconfigController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/rest/MaskPictaskconfigController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/rest/MaskVideotaskconfigController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/rest/MaskVideotaskconfigController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/service/MaskPictaskconfigService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/service/MaskPictaskconfigService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/service/MaskVideotaskconfigService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/service/MaskVideotaskconfigService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/service/dto/MaskPictaskconfigDto.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/service/dto/MaskPictaskconfigDto.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/service/dto/MaskPictaskconfigQueryCriteria.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/service/dto/MaskPictaskconfigQueryCriteria.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/service/dto/MaskVideotaskconfigDto.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/service/dto/MaskVideotaskconfigDto.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/service/dto/MaskVideotaskconfigQueryCriteria.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/service/dto/MaskVideotaskconfigQueryCriteria.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/service/impl/MaskPictaskconfigServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/service/impl/MaskPictaskconfigServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/service/impl/MaskVideotaskconfigServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/mask/service/impl/MaskVideotaskconfigServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/quartz/config/ScanInstantiation.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/quartz/config/ScanInstantiation.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/quartz/utils/ExecutionJob.java" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/java/com/wzsec/modules/quartz/utils/ExecutionJob.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_system/src/main/resources/config/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/resources/config/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ds_bdms_system/src/main/resources/ds_bdms_system.properties" beforeDir="false" afterPath="$PROJECT_DIR$/ds_bdms_system/src/main/resources/ds_bdms_system.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../ds_bdms_web/src/views/sdd/mask/maskPictaskconfig/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../ds_bdms_web/src/views/sdd/mask/maskPictaskconfig/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../ds_bdms_web/src/views/sdd/mask/maskVideotaskconfig/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../ds_bdms_web/src/views/sdd/mask/maskVideotaskconfig/index.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="COMPILER_PROCESS_HEAP_SIZE" value="3072" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\develop\apache-maven-3.6.1" />
        <option name="localRepository" value="D:\develop\maven_repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\develop\apache-maven-3.6.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="30d3inz9r6kpgl9MJqLEJ9E6yy3" />
  <component name="ProjectViewState">
    <option name="flattenModules" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.ds_bdms [clean].executor": "Run",
    "Maven.ds_bdms [package].executor": "Run",
    "Maven.ds_bdms_engine [clean].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.BDMSEngineRun.executor": "Debug",
    "Spring Boot.BDMSSystemRun.executor": "Debug",
    "git-widget-placeholder": "jiangyuan",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "D:/develop/work_project/gitee_ds_bdms_staticmask/ds_bdms",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "project.propCompiler",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.BDMSEngineRun">
    <configuration name="BDMSEngineRun" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ds_bdms_engine" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.wzsec.BDMSEngineRun" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BDMSSystemRun" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ds_bdms_system" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.wzsec.BDMSSystemRun" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="cb264ad8-4cc2-47ef-8691-64a929f29e97" name="更改" comment="" />
      <created>1753943751681</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753943751681</updated>
      <workItem from="1753943752855" duration="409000" />
      <workItem from="1753944190454" duration="11290000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ds_bdms_engine/src/main/java/com/wzsec/dotask/mask/service/impl/DoPictureTaskServiceImpl.java</url>
          <line>177</line>
          <option name="timeStamp" value="8" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>