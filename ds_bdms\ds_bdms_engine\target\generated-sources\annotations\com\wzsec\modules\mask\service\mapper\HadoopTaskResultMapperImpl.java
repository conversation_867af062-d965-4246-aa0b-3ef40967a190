package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.HadoopTaskResult;
import com.wzsec.modules.mask.service.dto.HadoopTaskResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T18:00:04+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class HadoopTaskResultMapperImpl implements HadoopTaskResultMapper {

    @Override
    public HadoopTaskResult toEntity(HadoopTaskResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        HadoopTaskResult hadoopTaskResult = new HadoopTaskResult();

        hadoopTaskResult.setId( dto.getId() );
        hadoopTaskResult.setUsername( dto.getUsername() );
        hadoopTaskResult.setPlatform( dto.getPlatform() );
        hadoopTaskResult.setFileformat( dto.getFileformat() );
        hadoopTaskResult.setDatainputpath( dto.getDatainputpath() );
        hadoopTaskResult.setDataoutputpath( dto.getDataoutputpath() );
        hadoopTaskResult.setDatafileinputpath( dto.getDatafileinputpath() );
        hadoopTaskResult.setDatafileoutputpath( dto.getDatafileoutputpath() );
        hadoopTaskResult.setDatasplit( dto.getDatasplit() );
        hadoopTaskResult.setConfigargs( dto.getConfigargs() );
        hadoopTaskResult.setJobstarttime( dto.getJobstarttime() );
        hadoopTaskResult.setJobendtime( dto.getJobendtime() );
        hadoopTaskResult.setJobtotaltime( dto.getJobtotaltime() );
        hadoopTaskResult.setJobstatus( dto.getJobstatus() );
        hadoopTaskResult.setDatarows( dto.getDatarows() );
        hadoopTaskResult.setCreatetime( dto.getCreatetime() );
        hadoopTaskResult.setUpdatetime( dto.getUpdatetime() );
        hadoopTaskResult.setBeforemaskdata( dto.getBeforemaskdata() );
        hadoopTaskResult.setAftermaskdata( dto.getAftermaskdata() );
        hadoopTaskResult.setRemark( dto.getRemark() );
        hadoopTaskResult.setSparefield1( dto.getSparefield1() );
        hadoopTaskResult.setSparefield2( dto.getSparefield2() );
        hadoopTaskResult.setSparefield3( dto.getSparefield3() );
        hadoopTaskResult.setSparefield4( dto.getSparefield4() );
        hadoopTaskResult.setTaskname( dto.getTaskname() );

        return hadoopTaskResult;
    }

    @Override
    public HadoopTaskResultDto toDto(HadoopTaskResult entity) {
        if ( entity == null ) {
            return null;
        }

        HadoopTaskResultDto hadoopTaskResultDto = new HadoopTaskResultDto();

        hadoopTaskResultDto.setId( entity.getId() );
        hadoopTaskResultDto.setUsername( entity.getUsername() );
        hadoopTaskResultDto.setPlatform( entity.getPlatform() );
        hadoopTaskResultDto.setFileformat( entity.getFileformat() );
        hadoopTaskResultDto.setDatainputpath( entity.getDatainputpath() );
        hadoopTaskResultDto.setDataoutputpath( entity.getDataoutputpath() );
        hadoopTaskResultDto.setDatafileinputpath( entity.getDatafileinputpath() );
        hadoopTaskResultDto.setDatafileoutputpath( entity.getDatafileoutputpath() );
        hadoopTaskResultDto.setDatasplit( entity.getDatasplit() );
        hadoopTaskResultDto.setConfigargs( entity.getConfigargs() );
        hadoopTaskResultDto.setJobstarttime( entity.getJobstarttime() );
        hadoopTaskResultDto.setJobendtime( entity.getJobendtime() );
        hadoopTaskResultDto.setJobtotaltime( entity.getJobtotaltime() );
        hadoopTaskResultDto.setJobstatus( entity.getJobstatus() );
        hadoopTaskResultDto.setDatarows( entity.getDatarows() );
        hadoopTaskResultDto.setCreatetime( entity.getCreatetime() );
        hadoopTaskResultDto.setUpdatetime( entity.getUpdatetime() );
        hadoopTaskResultDto.setBeforemaskdata( entity.getBeforemaskdata() );
        hadoopTaskResultDto.setAftermaskdata( entity.getAftermaskdata() );
        hadoopTaskResultDto.setRemark( entity.getRemark() );
        hadoopTaskResultDto.setSparefield1( entity.getSparefield1() );
        hadoopTaskResultDto.setSparefield2( entity.getSparefield2() );
        hadoopTaskResultDto.setSparefield3( entity.getSparefield3() );
        hadoopTaskResultDto.setSparefield4( entity.getSparefield4() );
        hadoopTaskResultDto.setTaskname( entity.getTaskname() );

        return hadoopTaskResultDto;
    }

    @Override
    public List<HadoopTaskResult> toEntity(List<HadoopTaskResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<HadoopTaskResult> list = new ArrayList<HadoopTaskResult>( dtoList.size() );
        for ( HadoopTaskResultDto hadoopTaskResultDto : dtoList ) {
            list.add( toEntity( hadoopTaskResultDto ) );
        }

        return list;
    }

    @Override
    public List<HadoopTaskResultDto> toDto(List<HadoopTaskResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<HadoopTaskResultDto> list = new ArrayList<HadoopTaskResultDto>( entityList.size() );
        for ( HadoopTaskResult hadoopTaskResult : entityList ) {
            list.add( toDto( hadoopTaskResult ) );
        }

        return list;
    }
}
