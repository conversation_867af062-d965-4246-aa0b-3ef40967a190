package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskStrategyFileMain;
import com.wzsec.modules.mask.service.dto.MaskStrategyFileMainDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T18:00:03+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskStrategyFileMainMapperImpl implements MaskStrategyFileMainMapper {

    @Override
    public MaskStrategyFileMain toEntity(MaskStrategyFileMainDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskStrategyFileMain maskStrategyFileMain = new MaskStrategyFileMain();

        maskStrategyFileMain.setId( dto.getId() );
        maskStrategyFileMain.setStrategyname( dto.getStrategyname() );
        maskStrategyFileMain.setStrategydesc( dto.getStrategydesc() );
        maskStrategyFileMain.setStrategytype( dto.getStrategytype() );
        maskStrategyFileMain.setTotalcolumn( dto.getTotalcolumn() );
        maskStrategyFileMain.setExtractcolum( dto.getExtractcolum() );
        maskStrategyFileMain.setStatus( dto.getStatus() );
        maskStrategyFileMain.setCreateuser( dto.getCreateuser() );
        maskStrategyFileMain.setCreatetime( dto.getCreatetime() );
        maskStrategyFileMain.setUpdateuser( dto.getUpdateuser() );
        maskStrategyFileMain.setUpdatetime( dto.getUpdatetime() );
        maskStrategyFileMain.setRemark( dto.getRemark() );
        maskStrategyFileMain.setSparefield1( dto.getSparefield1() );
        maskStrategyFileMain.setSparefield2( dto.getSparefield2() );
        maskStrategyFileMain.setSparefield3( dto.getSparefield3() );
        maskStrategyFileMain.setSparefield4( dto.getSparefield4() );

        return maskStrategyFileMain;
    }

    @Override
    public MaskStrategyFileMainDto toDto(MaskStrategyFileMain entity) {
        if ( entity == null ) {
            return null;
        }

        MaskStrategyFileMainDto maskStrategyFileMainDto = new MaskStrategyFileMainDto();

        maskStrategyFileMainDto.setId( entity.getId() );
        maskStrategyFileMainDto.setStrategyname( entity.getStrategyname() );
        maskStrategyFileMainDto.setStrategydesc( entity.getStrategydesc() );
        maskStrategyFileMainDto.setStrategytype( entity.getStrategytype() );
        maskStrategyFileMainDto.setTotalcolumn( entity.getTotalcolumn() );
        maskStrategyFileMainDto.setExtractcolum( entity.getExtractcolum() );
        maskStrategyFileMainDto.setStatus( entity.getStatus() );
        maskStrategyFileMainDto.setCreateuser( entity.getCreateuser() );
        maskStrategyFileMainDto.setCreatetime( entity.getCreatetime() );
        maskStrategyFileMainDto.setUpdateuser( entity.getUpdateuser() );
        maskStrategyFileMainDto.setUpdatetime( entity.getUpdatetime() );
        maskStrategyFileMainDto.setRemark( entity.getRemark() );
        maskStrategyFileMainDto.setSparefield1( entity.getSparefield1() );
        maskStrategyFileMainDto.setSparefield2( entity.getSparefield2() );
        maskStrategyFileMainDto.setSparefield3( entity.getSparefield3() );
        maskStrategyFileMainDto.setSparefield4( entity.getSparefield4() );

        return maskStrategyFileMainDto;
    }

    @Override
    public List<MaskStrategyFileMain> toEntity(List<MaskStrategyFileMainDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskStrategyFileMain> list = new ArrayList<MaskStrategyFileMain>( dtoList.size() );
        for ( MaskStrategyFileMainDto maskStrategyFileMainDto : dtoList ) {
            list.add( toEntity( maskStrategyFileMainDto ) );
        }

        return list;
    }

    @Override
    public List<MaskStrategyFileMainDto> toDto(List<MaskStrategyFileMain> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskStrategyFileMainDto> list = new ArrayList<MaskStrategyFileMainDto>( entityList.size() );
        for ( MaskStrategyFileMain maskStrategyFileMain : entityList ) {
            list.add( toDto( maskStrategyFileMain ) );
        }

        return list;
    }
}
