package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskPictaskconfig;
import com.wzsec.modules.mask.service.dto.MaskPictaskconfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T18:00:03+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskPictaskconfigMapperImpl implements MaskPictaskconfigMapper {

    @Override
    public MaskPictaskconfig toEntity(MaskPictaskconfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskPictaskconfig maskPictaskconfig = new MaskPictaskconfig();

        maskPictaskconfig.setId( dto.getId() );
        maskPictaskconfig.setTaskname( dto.getTaskname() );
        maskPictaskconfig.setInputdirectory( dto.getInputdirectory() );
        maskPictaskconfig.setInputfileformat( dto.getInputfileformat() );
        maskPictaskconfig.setComputeresources( dto.getComputeresources() );
        maskPictaskconfig.setMaskobject( dto.getMaskobject() );
        maskPictaskconfig.setState( dto.getState() );
        maskPictaskconfig.setOutputfileformat( dto.getOutputfileformat() );
        maskPictaskconfig.setOutputdirectory( dto.getOutputdirectory() );
        maskPictaskconfig.setExecutionstate( dto.getExecutionstate() );
        maskPictaskconfig.setSubmitmethod( dto.getSubmitmethod() );
        maskPictaskconfig.setCron( dto.getCron() );
        maskPictaskconfig.setCreateuser( dto.getCreateuser() );
        maskPictaskconfig.setCreatetime( dto.getCreatetime() );
        maskPictaskconfig.setUpdateuser( dto.getUpdateuser() );
        maskPictaskconfig.setUpdatetime( dto.getUpdatetime() );
        maskPictaskconfig.setRemark( dto.getRemark() );
        maskPictaskconfig.setSparefield1( dto.getSparefield1() );
        maskPictaskconfig.setSparefield2( dto.getSparefield2() );
        maskPictaskconfig.setSparefield3( dto.getSparefield3() );
        maskPictaskconfig.setSparefield4( dto.getSparefield4() );
        maskPictaskconfig.setSparefield5( dto.getSparefield5() );

        return maskPictaskconfig;
    }

    @Override
    public MaskPictaskconfigDto toDto(MaskPictaskconfig entity) {
        if ( entity == null ) {
            return null;
        }

        MaskPictaskconfigDto maskPictaskconfigDto = new MaskPictaskconfigDto();

        maskPictaskconfigDto.setId( entity.getId() );
        maskPictaskconfigDto.setTaskname( entity.getTaskname() );
        maskPictaskconfigDto.setInputdirectory( entity.getInputdirectory() );
        maskPictaskconfigDto.setInputfileformat( entity.getInputfileformat() );
        maskPictaskconfigDto.setComputeresources( entity.getComputeresources() );
        maskPictaskconfigDto.setMaskobject( entity.getMaskobject() );
        maskPictaskconfigDto.setState( entity.getState() );
        maskPictaskconfigDto.setOutputfileformat( entity.getOutputfileformat() );
        maskPictaskconfigDto.setOutputdirectory( entity.getOutputdirectory() );
        maskPictaskconfigDto.setExecutionstate( entity.getExecutionstate() );
        maskPictaskconfigDto.setSubmitmethod( entity.getSubmitmethod() );
        maskPictaskconfigDto.setCron( entity.getCron() );
        maskPictaskconfigDto.setCreateuser( entity.getCreateuser() );
        maskPictaskconfigDto.setCreatetime( entity.getCreatetime() );
        maskPictaskconfigDto.setUpdateuser( entity.getUpdateuser() );
        maskPictaskconfigDto.setUpdatetime( entity.getUpdatetime() );
        maskPictaskconfigDto.setRemark( entity.getRemark() );
        maskPictaskconfigDto.setSparefield1( entity.getSparefield1() );
        maskPictaskconfigDto.setSparefield2( entity.getSparefield2() );
        maskPictaskconfigDto.setSparefield3( entity.getSparefield3() );
        maskPictaskconfigDto.setSparefield4( entity.getSparefield4() );
        maskPictaskconfigDto.setSparefield5( entity.getSparefield5() );

        return maskPictaskconfigDto;
    }

    @Override
    public List<MaskPictaskconfig> toEntity(List<MaskPictaskconfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskPictaskconfig> list = new ArrayList<MaskPictaskconfig>( dtoList.size() );
        for ( MaskPictaskconfigDto maskPictaskconfigDto : dtoList ) {
            list.add( toEntity( maskPictaskconfigDto ) );
        }

        return list;
    }

    @Override
    public List<MaskPictaskconfigDto> toDto(List<MaskPictaskconfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskPictaskconfigDto> list = new ArrayList<MaskPictaskconfigDto>( entityList.size() );
        for ( MaskPictaskconfig maskPictaskconfig : entityList ) {
            list.add( toDto( maskPictaskconfig ) );
        }

        return list;
    }
}
