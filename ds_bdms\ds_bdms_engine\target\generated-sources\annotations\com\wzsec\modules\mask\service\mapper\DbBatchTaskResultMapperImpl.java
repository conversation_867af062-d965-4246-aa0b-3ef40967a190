package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.DbBatchTaskResult;
import com.wzsec.modules.mask.service.dto.DbBatchTaskResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T18:00:03+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class DbBatchTaskResultMapperImpl implements DbBatchTaskResultMapper {

    @Override
    public DbBatchTaskResult toEntity(DbBatchTaskResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        DbBatchTaskResult dbBatchTaskResult = new DbBatchTaskResult();

        dbBatchTaskResult.setId( dto.getId() );
        dbBatchTaskResult.setTaskname( dto.getTaskname() );
        dbBatchTaskResult.setTabname( dto.getTabname() );
        dbBatchTaskResult.setDbname( dto.getDbname() );
        dbBatchTaskResult.setIpaddress( dto.getIpaddress() );
        dbBatchTaskResult.setOutputname( dto.getOutputname() );
        dbBatchTaskResult.setOutputpath( dto.getOutputpath() );
        dbBatchTaskResult.setOutputipaddress( dto.getOutputipaddress() );
        dbBatchTaskResult.setOutputtype( dto.getOutputtype() );
        dbBatchTaskResult.setTotallines( dto.getTotallines() );
        dbBatchTaskResult.setMasklines( dto.getMasklines() );
        dbBatchTaskResult.setLastmasklines( dto.getLastmasklines() );
        dbBatchTaskResult.setBeforemaskdata( dto.getBeforemaskdata() );
        dbBatchTaskResult.setAftermaskdata( dto.getAftermaskdata() );
        dbBatchTaskResult.setStrategyjson( dto.getStrategyjson() );
        dbBatchTaskResult.setTaskstatus( dto.getTaskstatus() );
        dbBatchTaskResult.setUsername( dto.getUsername() );
        dbBatchTaskResult.setJobstarttime( dto.getJobstarttime() );
        dbBatchTaskResult.setJobendtime( dto.getJobendtime() );
        dbBatchTaskResult.setJobtotaltime( dto.getJobtotaltime() );
        dbBatchTaskResult.setRemark( dto.getRemark() );
        dbBatchTaskResult.setSparefield1( dto.getSparefield1() );
        dbBatchTaskResult.setSparefield2( dto.getSparefield2() );
        dbBatchTaskResult.setSparefield3( dto.getSparefield3() );
        dbBatchTaskResult.setSparefield4( dto.getSparefield4() );
        dbBatchTaskResult.setSparefield5( dto.getSparefield5() );

        return dbBatchTaskResult;
    }

    @Override
    public DbBatchTaskResultDto toDto(DbBatchTaskResult entity) {
        if ( entity == null ) {
            return null;
        }

        DbBatchTaskResultDto dbBatchTaskResultDto = new DbBatchTaskResultDto();

        dbBatchTaskResultDto.setId( entity.getId() );
        dbBatchTaskResultDto.setTaskname( entity.getTaskname() );
        dbBatchTaskResultDto.setTabname( entity.getTabname() );
        dbBatchTaskResultDto.setDbname( entity.getDbname() );
        dbBatchTaskResultDto.setIpaddress( entity.getIpaddress() );
        dbBatchTaskResultDto.setOutputname( entity.getOutputname() );
        dbBatchTaskResultDto.setOutputpath( entity.getOutputpath() );
        dbBatchTaskResultDto.setOutputipaddress( entity.getOutputipaddress() );
        dbBatchTaskResultDto.setOutputtype( entity.getOutputtype() );
        dbBatchTaskResultDto.setTotallines( entity.getTotallines() );
        dbBatchTaskResultDto.setMasklines( entity.getMasklines() );
        dbBatchTaskResultDto.setLastmasklines( entity.getLastmasklines() );
        dbBatchTaskResultDto.setBeforemaskdata( entity.getBeforemaskdata() );
        dbBatchTaskResultDto.setAftermaskdata( entity.getAftermaskdata() );
        dbBatchTaskResultDto.setStrategyjson( entity.getStrategyjson() );
        dbBatchTaskResultDto.setTaskstatus( entity.getTaskstatus() );
        dbBatchTaskResultDto.setUsername( entity.getUsername() );
        dbBatchTaskResultDto.setJobstarttime( entity.getJobstarttime() );
        dbBatchTaskResultDto.setJobendtime( entity.getJobendtime() );
        dbBatchTaskResultDto.setJobtotaltime( entity.getJobtotaltime() );
        dbBatchTaskResultDto.setRemark( entity.getRemark() );
        dbBatchTaskResultDto.setSparefield1( entity.getSparefield1() );
        dbBatchTaskResultDto.setSparefield2( entity.getSparefield2() );
        dbBatchTaskResultDto.setSparefield3( entity.getSparefield3() );
        dbBatchTaskResultDto.setSparefield4( entity.getSparefield4() );
        dbBatchTaskResultDto.setSparefield5( entity.getSparefield5() );

        return dbBatchTaskResultDto;
    }

    @Override
    public List<DbBatchTaskResult> toEntity(List<DbBatchTaskResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<DbBatchTaskResult> list = new ArrayList<DbBatchTaskResult>( dtoList.size() );
        for ( DbBatchTaskResultDto dbBatchTaskResultDto : dtoList ) {
            list.add( toEntity( dbBatchTaskResultDto ) );
        }

        return list;
    }

    @Override
    public List<DbBatchTaskResultDto> toDto(List<DbBatchTaskResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DbBatchTaskResultDto> list = new ArrayList<DbBatchTaskResultDto>( entityList.size() );
        for ( DbBatchTaskResult dbBatchTaskResult : entityList ) {
            list.add( toDto( dbBatchTaskResult ) );
        }

        return list;
    }
}
