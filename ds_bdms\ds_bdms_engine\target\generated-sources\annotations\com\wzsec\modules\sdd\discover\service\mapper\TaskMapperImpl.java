package com.wzsec.modules.sdd.discover.service.mapper;

import com.wzsec.modules.sdd.discover.domain.Task;
import com.wzsec.modules.sdd.discover.service.dto.TaskDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T18:00:04+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class TaskMapperImpl implements TaskMapper {

    @Override
    public Task toEntity(TaskDto dto) {
        if ( dto == null ) {
            return null;
        }

        Task task = new Task();

        task.setId( dto.getId() );
        task.setTaskname( dto.getTaskname() );
        task.setDatatype( dto.getDatatype() );
        task.setDatasourceid( dto.getDatasourceid() );
        task.setDatapath( dto.getDatapath() );
        task.setStrategyids( dto.getStrategyids() );
        task.setSubmittype( dto.getSubmittype() );
        task.setCron( dto.getCron() );
        task.setExecutestate( dto.getExecutestate() );
        task.setState( dto.getState() );
        task.setNote( dto.getNote() );
        task.setCreateuser( dto.getCreateuser() );
        task.setCreatetime( dto.getCreatetime() );
        task.setUpdateuser( dto.getUpdateuser() );
        task.setUpdatetime( dto.getUpdatetime() );
        task.setSparefield1( dto.getSparefield1() );
        task.setSparefield2( dto.getSparefield2() );
        task.setSparefield3( dto.getSparefield3() );
        task.setSparefield4( dto.getSparefield4() );
        task.setSparefield5( dto.getSparefield5() );

        return task;
    }

    @Override
    public TaskDto toDto(Task entity) {
        if ( entity == null ) {
            return null;
        }

        TaskDto taskDto = new TaskDto();

        taskDto.setId( entity.getId() );
        taskDto.setTaskname( entity.getTaskname() );
        taskDto.setDatatype( entity.getDatatype() );
        taskDto.setDatasourceid( entity.getDatasourceid() );
        taskDto.setDatapath( entity.getDatapath() );
        taskDto.setStrategyids( entity.getStrategyids() );
        taskDto.setSubmittype( entity.getSubmittype() );
        taskDto.setCron( entity.getCron() );
        taskDto.setExecutestate( entity.getExecutestate() );
        taskDto.setState( entity.getState() );
        taskDto.setNote( entity.getNote() );
        taskDto.setCreateuser( entity.getCreateuser() );
        taskDto.setCreatetime( entity.getCreatetime() );
        taskDto.setUpdateuser( entity.getUpdateuser() );
        taskDto.setUpdatetime( entity.getUpdatetime() );
        taskDto.setSparefield1( entity.getSparefield1() );
        taskDto.setSparefield2( entity.getSparefield2() );
        taskDto.setSparefield3( entity.getSparefield3() );
        taskDto.setSparefield4( entity.getSparefield4() );
        taskDto.setSparefield5( entity.getSparefield5() );

        return taskDto;
    }

    @Override
    public List<Task> toEntity(List<TaskDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Task> list = new ArrayList<Task>( dtoList.size() );
        for ( TaskDto taskDto : dtoList ) {
            list.add( toEntity( taskDto ) );
        }

        return list;
    }

    @Override
    public List<TaskDto> toDto(List<Task> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TaskDto> list = new ArrayList<TaskDto>( entityList.size() );
        for ( Task task : entityList ) {
            list.add( toDto( task ) );
        }

        return list;
    }
}
