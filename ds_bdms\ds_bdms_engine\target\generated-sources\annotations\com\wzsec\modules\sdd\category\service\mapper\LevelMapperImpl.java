package com.wzsec.modules.sdd.category.service.mapper;

import com.wzsec.modules.sdd.category.domain.Level;
import com.wzsec.modules.sdd.category.service.dto.LevelDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T18:00:04+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class LevelMapperImpl implements LevelMapper {

    @Override
    public Level toEntity(LevelDto dto) {
        if ( dto == null ) {
            return null;
        }

        Level level = new Level();

        level.setId( dto.getId() );
        level.setLevelmeaning( dto.getLevelmeaning() );
        level.setCreateuser( dto.getCreateuser() );
        level.setCreatetime( dto.getCreatetime() );
        level.setUpdateuser( dto.getUpdateuser() );
        level.setUpdatetime( dto.getUpdatetime() );
        level.setSparefield1( dto.getSparefield1() );
        level.setSparefield2( dto.getSparefield2() );
        level.setSparefield3( dto.getSparefield3() );
        level.setSparefield4( dto.getSparefield4() );
        level.setSparefield5( dto.getSparefield5() );
        level.setLevelcode( dto.getLevelcode() );

        return level;
    }

    @Override
    public LevelDto toDto(Level entity) {
        if ( entity == null ) {
            return null;
        }

        LevelDto levelDto = new LevelDto();

        levelDto.setId( entity.getId() );
        levelDto.setLevelmeaning( entity.getLevelmeaning() );
        levelDto.setCreateuser( entity.getCreateuser() );
        levelDto.setCreatetime( entity.getCreatetime() );
        levelDto.setUpdateuser( entity.getUpdateuser() );
        levelDto.setUpdatetime( entity.getUpdatetime() );
        levelDto.setSparefield1( entity.getSparefield1() );
        levelDto.setSparefield2( entity.getSparefield2() );
        levelDto.setSparefield3( entity.getSparefield3() );
        levelDto.setSparefield4( entity.getSparefield4() );
        levelDto.setSparefield5( entity.getSparefield5() );
        levelDto.setLevelcode( entity.getLevelcode() );

        return levelDto;
    }

    @Override
    public List<Level> toEntity(List<LevelDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Level> list = new ArrayList<Level>( dtoList.size() );
        for ( LevelDto levelDto : dtoList ) {
            list.add( toEntity( levelDto ) );
        }

        return list;
    }

    @Override
    public List<LevelDto> toDto(List<Level> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<LevelDto> list = new ArrayList<LevelDto>( entityList.size() );
        for ( Level level : entityList ) {
            list.add( toDto( level ) );
        }

        return list;
    }
}
