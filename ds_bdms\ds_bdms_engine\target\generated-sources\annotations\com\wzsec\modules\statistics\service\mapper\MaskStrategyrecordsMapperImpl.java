package com.wzsec.modules.statistics.service.mapper;

import com.wzsec.modules.statistics.domain.MaskStrategyrecords;
import com.wzsec.modules.statistics.service.dto.MaskStrategyrecordsDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T18:00:04+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskStrategyrecordsMapperImpl implements MaskStrategyrecordsMapper {

    @Override
    public MaskStrategyrecords toEntity(MaskStrategyrecordsDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskStrategyrecords maskStrategyrecords = new MaskStrategyrecords();

        maskStrategyrecords.setId( dto.getId() );
        maskStrategyrecords.setStrategyname( dto.getStrategyname() );
        maskStrategyrecords.setStrategydesc( dto.getStrategydesc() );
        maskStrategyrecords.setStrategytype( dto.getStrategytype() );
        maskStrategyrecords.setDbname( dto.getDbname() );
        maskStrategyrecords.setTabid( dto.getTabid() );
        maskStrategyrecords.setTabename( dto.getTabename() );
        maskStrategyrecords.setFieldname( dto.getFieldname() );
        maskStrategyrecords.setFieldcname( dto.getFieldcname() );
        maskStrategyrecords.setRulename( dto.getRulename() );
        maskStrategyrecords.setAlgorithmname( dto.getAlgorithmname() );
        maskStrategyrecords.setOperation( dto.getOperation() );
        maskStrategyrecords.setCreateuser( dto.getCreateuser() );
        maskStrategyrecords.setCreatetime( dto.getCreatetime() );
        maskStrategyrecords.setSparefield1( dto.getSparefield1() );
        maskStrategyrecords.setSparefield2( dto.getSparefield2() );
        maskStrategyrecords.setSparefield3( dto.getSparefield3() );
        maskStrategyrecords.setSparefield4( dto.getSparefield4() );

        return maskStrategyrecords;
    }

    @Override
    public MaskStrategyrecordsDto toDto(MaskStrategyrecords entity) {
        if ( entity == null ) {
            return null;
        }

        MaskStrategyrecordsDto maskStrategyrecordsDto = new MaskStrategyrecordsDto();

        maskStrategyrecordsDto.setId( entity.getId() );
        maskStrategyrecordsDto.setStrategyname( entity.getStrategyname() );
        maskStrategyrecordsDto.setStrategydesc( entity.getStrategydesc() );
        maskStrategyrecordsDto.setStrategytype( entity.getStrategytype() );
        maskStrategyrecordsDto.setDbname( entity.getDbname() );
        maskStrategyrecordsDto.setTabid( entity.getTabid() );
        maskStrategyrecordsDto.setTabename( entity.getTabename() );
        maskStrategyrecordsDto.setFieldname( entity.getFieldname() );
        maskStrategyrecordsDto.setFieldcname( entity.getFieldcname() );
        maskStrategyrecordsDto.setRulename( entity.getRulename() );
        maskStrategyrecordsDto.setAlgorithmname( entity.getAlgorithmname() );
        maskStrategyrecordsDto.setOperation( entity.getOperation() );
        maskStrategyrecordsDto.setCreateuser( entity.getCreateuser() );
        maskStrategyrecordsDto.setCreatetime( entity.getCreatetime() );
        maskStrategyrecordsDto.setSparefield1( entity.getSparefield1() );
        maskStrategyrecordsDto.setSparefield2( entity.getSparefield2() );
        maskStrategyrecordsDto.setSparefield3( entity.getSparefield3() );
        maskStrategyrecordsDto.setSparefield4( entity.getSparefield4() );

        return maskStrategyrecordsDto;
    }

    @Override
    public List<MaskStrategyrecords> toEntity(List<MaskStrategyrecordsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskStrategyrecords> list = new ArrayList<MaskStrategyrecords>( dtoList.size() );
        for ( MaskStrategyrecordsDto maskStrategyrecordsDto : dtoList ) {
            list.add( toEntity( maskStrategyrecordsDto ) );
        }

        return list;
    }

    @Override
    public List<MaskStrategyrecordsDto> toDto(List<MaskStrategyrecords> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskStrategyrecordsDto> list = new ArrayList<MaskStrategyrecordsDto>( entityList.size() );
        for ( MaskStrategyrecords maskStrategyrecords : entityList ) {
            list.add( toDto( maskStrategyrecords ) );
        }

        return list;
    }
}
