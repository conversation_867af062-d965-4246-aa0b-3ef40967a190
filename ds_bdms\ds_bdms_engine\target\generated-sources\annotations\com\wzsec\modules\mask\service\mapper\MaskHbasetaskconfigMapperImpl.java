package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskHbasetaskconfig;
import com.wzsec.modules.mask.service.dto.MaskHbasetaskconfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T18:00:03+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskHbasetaskconfigMapperImpl implements MaskHbasetaskconfigMapper {

    @Override
    public MaskHbasetaskconfig toEntity(MaskHbasetaskconfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskHbasetaskconfig maskHbasetaskconfig = new MaskHbasetaskconfig();

        maskHbasetaskconfig.setId( dto.getId() );
        maskHbasetaskconfig.setWorksheet( dto.getWorksheet() );
        maskHbasetaskconfig.setBatchnumber( dto.getBatchnumber() );
        maskHbasetaskconfig.setDbname( dto.getDbname() );
        maskHbasetaskconfig.setTabname( dto.getTabname() );
        maskHbasetaskconfig.setStrategyname( dto.getStrategyname() );
        maskHbasetaskconfig.setQueuename( dto.getQueuename() );
        maskHbasetaskconfig.setCount( dto.getCount() );
        maskHbasetaskconfig.setOutputtype( dto.getOutputtype() );
        maskHbasetaskconfig.setOutputtablename( dto.getOutputtablename() );
        maskHbasetaskconfig.setDatasplit( dto.getDatasplit() );
        maskHbasetaskconfig.setDataoutputdir( dto.getDataoutputdir() );
        maskHbasetaskconfig.setStatus( dto.getStatus() );
        maskHbasetaskconfig.setUsername( dto.getUsername() );
        maskHbasetaskconfig.setStrategystr( dto.getStrategystr() );
        maskHbasetaskconfig.setCreateuserid( dto.getCreateuserid() );
        maskHbasetaskconfig.setCreatetime( dto.getCreatetime() );
        maskHbasetaskconfig.setUpdateuserid( dto.getUpdateuserid() );
        maskHbasetaskconfig.setUpdatetime( dto.getUpdatetime() );
        maskHbasetaskconfig.setRemark( dto.getRemark() );
        maskHbasetaskconfig.setSparefield1( dto.getSparefield1() );
        maskHbasetaskconfig.setSparefield2( dto.getSparefield2() );
        maskHbasetaskconfig.setSparefield3( dto.getSparefield3() );
        maskHbasetaskconfig.setSparefield4( dto.getSparefield4() );

        return maskHbasetaskconfig;
    }

    @Override
    public MaskHbasetaskconfigDto toDto(MaskHbasetaskconfig entity) {
        if ( entity == null ) {
            return null;
        }

        MaskHbasetaskconfigDto maskHbasetaskconfigDto = new MaskHbasetaskconfigDto();

        maskHbasetaskconfigDto.setId( entity.getId() );
        maskHbasetaskconfigDto.setWorksheet( entity.getWorksheet() );
        maskHbasetaskconfigDto.setBatchnumber( entity.getBatchnumber() );
        maskHbasetaskconfigDto.setDbname( entity.getDbname() );
        maskHbasetaskconfigDto.setTabname( entity.getTabname() );
        maskHbasetaskconfigDto.setStrategyname( entity.getStrategyname() );
        maskHbasetaskconfigDto.setQueuename( entity.getQueuename() );
        maskHbasetaskconfigDto.setCount( entity.getCount() );
        maskHbasetaskconfigDto.setOutputtype( entity.getOutputtype() );
        maskHbasetaskconfigDto.setOutputtablename( entity.getOutputtablename() );
        maskHbasetaskconfigDto.setDatasplit( entity.getDatasplit() );
        maskHbasetaskconfigDto.setDataoutputdir( entity.getDataoutputdir() );
        maskHbasetaskconfigDto.setStatus( entity.getStatus() );
        maskHbasetaskconfigDto.setUsername( entity.getUsername() );
        maskHbasetaskconfigDto.setStrategystr( entity.getStrategystr() );
        maskHbasetaskconfigDto.setCreateuserid( entity.getCreateuserid() );
        maskHbasetaskconfigDto.setCreatetime( entity.getCreatetime() );
        maskHbasetaskconfigDto.setUpdateuserid( entity.getUpdateuserid() );
        maskHbasetaskconfigDto.setUpdatetime( entity.getUpdatetime() );
        maskHbasetaskconfigDto.setRemark( entity.getRemark() );
        maskHbasetaskconfigDto.setSparefield1( entity.getSparefield1() );
        maskHbasetaskconfigDto.setSparefield2( entity.getSparefield2() );
        maskHbasetaskconfigDto.setSparefield3( entity.getSparefield3() );
        maskHbasetaskconfigDto.setSparefield4( entity.getSparefield4() );

        return maskHbasetaskconfigDto;
    }

    @Override
    public List<MaskHbasetaskconfig> toEntity(List<MaskHbasetaskconfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskHbasetaskconfig> list = new ArrayList<MaskHbasetaskconfig>( dtoList.size() );
        for ( MaskHbasetaskconfigDto maskHbasetaskconfigDto : dtoList ) {
            list.add( toEntity( maskHbasetaskconfigDto ) );
        }

        return list;
    }

    @Override
    public List<MaskHbasetaskconfigDto> toDto(List<MaskHbasetaskconfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskHbasetaskconfigDto> list = new ArrayList<MaskHbasetaskconfigDto>( entityList.size() );
        for ( MaskHbasetaskconfig maskHbasetaskconfig : entityList ) {
            list.add( toDto( maskHbasetaskconfig ) );
        }

        return list;
    }
}
