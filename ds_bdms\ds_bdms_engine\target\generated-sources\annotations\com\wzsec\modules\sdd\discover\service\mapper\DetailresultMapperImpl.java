package com.wzsec.modules.sdd.discover.service.mapper;

import com.wzsec.modules.sdd.discover.domain.Detailresult;
import com.wzsec.modules.sdd.discover.service.dto.DetailresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T18:00:04+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class DetailresultMapperImpl implements DetailresultMapper {

    @Override
    public Detailresult toEntity(DetailresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        Detailresult detailresult = new Detailresult();

        detailresult.setId( dto.getId() );
        detailresult.setTaskname( dto.getTaskname() );
        detailresult.setSourcetype( dto.getSourcetype() );
        detailresult.setDborpath( dto.getDborpath() );
        detailresult.setTableorfile( dto.getTableorfile() );
        detailresult.setDatatype( dto.getDatatype() );
        if ( dto.getDatacount() != null ) {
            detailresult.setDatacount( Integer.parseInt( dto.getDatacount() ) );
        }
        detailresult.setExample( dto.getExample() );
        detailresult.setCreatetime( dto.getCreatetime() );
        if ( dto.getSparefield1() != null ) {
            detailresult.setSparefield1( Integer.parseInt( dto.getSparefield1() ) );
        }
        detailresult.setSparefield2( dto.getSparefield2() );
        detailresult.setSparefield3( dto.getSparefield3() );
        detailresult.setSparefield4( dto.getSparefield4() );
        detailresult.setSparefield5( dto.getSparefield5() );

        return detailresult;
    }

    @Override
    public DetailresultDto toDto(Detailresult entity) {
        if ( entity == null ) {
            return null;
        }

        DetailresultDto detailresultDto = new DetailresultDto();

        detailresultDto.setId( entity.getId() );
        detailresultDto.setTaskname( entity.getTaskname() );
        detailresultDto.setSourcetype( entity.getSourcetype() );
        detailresultDto.setDborpath( entity.getDborpath() );
        detailresultDto.setTableorfile( entity.getTableorfile() );
        detailresultDto.setDatatype( entity.getDatatype() );
        if ( entity.getDatacount() != null ) {
            detailresultDto.setDatacount( String.valueOf( entity.getDatacount() ) );
        }
        detailresultDto.setExample( entity.getExample() );
        detailresultDto.setCreatetime( entity.getCreatetime() );
        if ( entity.getSparefield1() != null ) {
            detailresultDto.setSparefield1( String.valueOf( entity.getSparefield1() ) );
        }
        detailresultDto.setSparefield2( entity.getSparefield2() );
        detailresultDto.setSparefield3( entity.getSparefield3() );
        detailresultDto.setSparefield4( entity.getSparefield4() );
        detailresultDto.setSparefield5( entity.getSparefield5() );

        return detailresultDto;
    }

    @Override
    public List<Detailresult> toEntity(List<DetailresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Detailresult> list = new ArrayList<Detailresult>( dtoList.size() );
        for ( DetailresultDto detailresultDto : dtoList ) {
            list.add( toEntity( detailresultDto ) );
        }

        return list;
    }

    @Override
    public List<DetailresultDto> toDto(List<Detailresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DetailresultDto> list = new ArrayList<DetailresultDto>( entityList.size() );
        for ( Detailresult detailresult : entityList ) {
            list.add( toDto( detailresult ) );
        }

        return list;
    }
}
