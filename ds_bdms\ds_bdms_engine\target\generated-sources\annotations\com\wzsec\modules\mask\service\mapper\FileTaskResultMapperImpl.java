package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.FileTaskResult;
import com.wzsec.modules.mask.service.dto.FileTaskResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T18:00:04+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class FileTaskResultMapperImpl implements FileTaskResultMapper {

    @Override
    public FileTaskResult toEntity(FileTaskResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        FileTaskResult fileTaskResult = new FileTaskResult();

        fileTaskResult.setId( dto.getId() );
        fileTaskResult.setTaskname( dto.getTaskname() );
        fileTaskResult.setInputpath( dto.getInputpath() );
        fileTaskResult.setInputfiletype( dto.getInputfiletype() );
        fileTaskResult.setSplitstr( dto.getSplitstr() );
        fileTaskResult.setStrategyname( dto.getStrategyname() );
        fileTaskResult.setOutputtype( dto.getOutputtype() );
        fileTaskResult.setOutputpath( dto.getOutputpath() );
        fileTaskResult.setOutputname( dto.getOutputname() );
        fileTaskResult.setTotallines( dto.getTotallines() );
        fileTaskResult.setBeforemaskdata( dto.getBeforemaskdata() );
        fileTaskResult.setAftermaskdata( dto.getAftermaskdata() );
        fileTaskResult.setTaskstatus( dto.getTaskstatus() );
        fileTaskResult.setUserid( dto.getUserid() );
        fileTaskResult.setUsername( dto.getUsername() );
        fileTaskResult.setJobstarttime( dto.getJobstarttime() );
        fileTaskResult.setJobendtime( dto.getJobendtime() );
        fileTaskResult.setJobtotaltime( dto.getJobtotaltime() );
        fileTaskResult.setCreatetime( dto.getCreatetime() );
        fileTaskResult.setUpdatetime( dto.getUpdatetime() );
        fileTaskResult.setRemark( dto.getRemark() );
        fileTaskResult.setSparefield1( dto.getSparefield1() );
        fileTaskResult.setSparefield2( dto.getSparefield2() );
        fileTaskResult.setSparefield3( dto.getSparefield3() );
        fileTaskResult.setSparefield4( dto.getSparefield4() );
        fileTaskResult.setSparefield5( dto.getSparefield5() );
        fileTaskResult.setFilenames( dto.getFilenames() );
        fileTaskResult.setMiniodata( dto.getMiniodata() );

        return fileTaskResult;
    }

    @Override
    public FileTaskResultDto toDto(FileTaskResult entity) {
        if ( entity == null ) {
            return null;
        }

        FileTaskResultDto fileTaskResultDto = new FileTaskResultDto();

        fileTaskResultDto.setId( entity.getId() );
        fileTaskResultDto.setTaskname( entity.getTaskname() );
        fileTaskResultDto.setInputpath( entity.getInputpath() );
        fileTaskResultDto.setInputfiletype( entity.getInputfiletype() );
        fileTaskResultDto.setSplitstr( entity.getSplitstr() );
        fileTaskResultDto.setStrategyname( entity.getStrategyname() );
        fileTaskResultDto.setOutputtype( entity.getOutputtype() );
        fileTaskResultDto.setOutputpath( entity.getOutputpath() );
        fileTaskResultDto.setOutputname( entity.getOutputname() );
        fileTaskResultDto.setTotallines( entity.getTotallines() );
        fileTaskResultDto.setBeforemaskdata( entity.getBeforemaskdata() );
        fileTaskResultDto.setAftermaskdata( entity.getAftermaskdata() );
        fileTaskResultDto.setTaskstatus( entity.getTaskstatus() );
        fileTaskResultDto.setUserid( entity.getUserid() );
        fileTaskResultDto.setUsername( entity.getUsername() );
        fileTaskResultDto.setJobstarttime( entity.getJobstarttime() );
        fileTaskResultDto.setJobendtime( entity.getJobendtime() );
        fileTaskResultDto.setJobtotaltime( entity.getJobtotaltime() );
        fileTaskResultDto.setCreatetime( entity.getCreatetime() );
        fileTaskResultDto.setUpdatetime( entity.getUpdatetime() );
        fileTaskResultDto.setRemark( entity.getRemark() );
        fileTaskResultDto.setSparefield1( entity.getSparefield1() );
        fileTaskResultDto.setSparefield2( entity.getSparefield2() );
        fileTaskResultDto.setSparefield3( entity.getSparefield3() );
        fileTaskResultDto.setSparefield4( entity.getSparefield4() );
        fileTaskResultDto.setSparefield5( entity.getSparefield5() );
        fileTaskResultDto.setFilenames( entity.getFilenames() );
        fileTaskResultDto.setMiniodata( entity.getMiniodata() );

        return fileTaskResultDto;
    }

    @Override
    public List<FileTaskResult> toEntity(List<FileTaskResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<FileTaskResult> list = new ArrayList<FileTaskResult>( dtoList.size() );
        for ( FileTaskResultDto fileTaskResultDto : dtoList ) {
            list.add( toEntity( fileTaskResultDto ) );
        }

        return list;
    }

    @Override
    public List<FileTaskResultDto> toDto(List<FileTaskResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<FileTaskResultDto> list = new ArrayList<FileTaskResultDto>( entityList.size() );
        for ( FileTaskResult fileTaskResult : entityList ) {
            list.add( toDto( fileTaskResult ) );
        }

        return list;
    }
}
