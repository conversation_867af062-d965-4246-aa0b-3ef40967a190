package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.HadoopTaskConfig;
import com.wzsec.modules.mask.service.dto.HadoopTaskConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T18:00:04+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class HadoopTaskConfigMapperImpl implements HadoopTaskConfigMapper {

    @Override
    public HadoopTaskConfig toEntity(HadoopTaskConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        HadoopTaskConfig hadoopTaskConfig = new HadoopTaskConfig();

        hadoopTaskConfig.setId( dto.getId() );
        hadoopTaskConfig.setTabdatabase( dto.getTabdatabase() );
        hadoopTaskConfig.setTabname( dto.getTabname() );
        hadoopTaskConfig.setPlatform( dto.getPlatform() );
        hadoopTaskConfig.setQueuename( dto.getQueuename() );
        hadoopTaskConfig.setFileformat( dto.getFileformat() );
        hadoopTaskConfig.setDatainputdir( dto.getDatainputdir() );
        hadoopTaskConfig.setDataoutputdir( dto.getDataoutputdir() );
        hadoopTaskConfig.setDatasplit( dto.getDatasplit() );
        hadoopTaskConfig.setStatus( dto.getStatus() );
        hadoopTaskConfig.setUsername( dto.getUsername() );
        hadoopTaskConfig.setCreatetime( dto.getCreatetime() );
        hadoopTaskConfig.setUpdatetime( dto.getUpdatetime() );
        hadoopTaskConfig.setRemark( dto.getRemark() );
        hadoopTaskConfig.setSparefield1( dto.getSparefield1() );
        hadoopTaskConfig.setSparefield2( dto.getSparefield2() );
        hadoopTaskConfig.setSparefield3( dto.getSparefield3() );
        hadoopTaskConfig.setSparefield4( dto.getSparefield4() );
        hadoopTaskConfig.setTaskname( dto.getTaskname() );
        hadoopTaskConfig.setDatasourcetype( dto.getDatasourcetype() );
        hadoopTaskConfig.setStrategyid( dto.getStrategyid() );
        hadoopTaskConfig.setFieldpostionalgoconfig( dto.getFieldpostionalgoconfig() );
        hadoopTaskConfig.setCreateuser( dto.getCreateuser() );
        hadoopTaskConfig.setUpdateuser( dto.getUpdateuser() );

        return hadoopTaskConfig;
    }

    @Override
    public HadoopTaskConfigDto toDto(HadoopTaskConfig entity) {
        if ( entity == null ) {
            return null;
        }

        HadoopTaskConfigDto hadoopTaskConfigDto = new HadoopTaskConfigDto();

        hadoopTaskConfigDto.setId( entity.getId() );
        hadoopTaskConfigDto.setTabdatabase( entity.getTabdatabase() );
        hadoopTaskConfigDto.setTabname( entity.getTabname() );
        hadoopTaskConfigDto.setPlatform( entity.getPlatform() );
        hadoopTaskConfigDto.setQueuename( entity.getQueuename() );
        hadoopTaskConfigDto.setFileformat( entity.getFileformat() );
        hadoopTaskConfigDto.setDatainputdir( entity.getDatainputdir() );
        hadoopTaskConfigDto.setDataoutputdir( entity.getDataoutputdir() );
        hadoopTaskConfigDto.setDatasplit( entity.getDatasplit() );
        hadoopTaskConfigDto.setStatus( entity.getStatus() );
        hadoopTaskConfigDto.setUsername( entity.getUsername() );
        hadoopTaskConfigDto.setCreatetime( entity.getCreatetime() );
        hadoopTaskConfigDto.setUpdatetime( entity.getUpdatetime() );
        hadoopTaskConfigDto.setRemark( entity.getRemark() );
        hadoopTaskConfigDto.setSparefield1( entity.getSparefield1() );
        hadoopTaskConfigDto.setSparefield2( entity.getSparefield2() );
        hadoopTaskConfigDto.setSparefield3( entity.getSparefield3() );
        hadoopTaskConfigDto.setSparefield4( entity.getSparefield4() );
        hadoopTaskConfigDto.setTaskname( entity.getTaskname() );
        hadoopTaskConfigDto.setDatasourcetype( entity.getDatasourcetype() );
        hadoopTaskConfigDto.setStrategyid( entity.getStrategyid() );
        hadoopTaskConfigDto.setFieldpostionalgoconfig( entity.getFieldpostionalgoconfig() );
        hadoopTaskConfigDto.setCreateuser( entity.getCreateuser() );
        hadoopTaskConfigDto.setUpdateuser( entity.getUpdateuser() );

        return hadoopTaskConfigDto;
    }

    @Override
    public List<HadoopTaskConfig> toEntity(List<HadoopTaskConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<HadoopTaskConfig> list = new ArrayList<HadoopTaskConfig>( dtoList.size() );
        for ( HadoopTaskConfigDto hadoopTaskConfigDto : dtoList ) {
            list.add( toEntity( hadoopTaskConfigDto ) );
        }

        return list;
    }

    @Override
    public List<HadoopTaskConfigDto> toDto(List<HadoopTaskConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<HadoopTaskConfigDto> list = new ArrayList<HadoopTaskConfigDto>( entityList.size() );
        for ( HadoopTaskConfig hadoopTaskConfig : entityList ) {
            list.add( toDto( hadoopTaskConfig ) );
        }

        return list;
    }
}
