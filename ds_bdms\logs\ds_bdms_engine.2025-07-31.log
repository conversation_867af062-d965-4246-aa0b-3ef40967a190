2025-07-31 11:30:41,168 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 16408 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-31 11:30:41,178 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-31 11:30:43,938 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-31 11:30:43,940 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 11:30:44,991 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1038 ms. Found 88 JPA repository interfaces.
2025-07-31 11:30:45,495 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-31 11:30:45,495 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 11:30:45,495 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:30:45,495 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:30:45,495 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:30:45,509 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-31 11:30:45,510 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-31 11:30:45,510 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:30:45,510 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:30:46,204 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-31 11:30:46,223 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-31 11:30:46,225 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-31 11:30:47,233 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-31 11:30:47,496 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-31 11:30:47,520 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-31 11:30:47,520 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-31 11:30:47,520 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-31 11:30:47,520 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-31 11:30:47,520 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-31 11:30:47,520 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-31 11:30:47,531 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-31 11:30:47,533 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-31 11:30:50,036 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-31 11:30:50,629 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 11:30:50,814 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-31 11:30:51,286 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 11:30:51,778 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-31 11:30:57,583 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 11:30:57,617 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 11:31:01,292 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-31 11:31:01,621 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 19633 ms
2025-07-31 11:31:05,621 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-31 11:31:06,330 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-31 11:31:06,511 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 11:31:06,598 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 11:31:06,600 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 11:31:06,620 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 11:31:06,625 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 11:31:06,625 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-31 11:31:06,625 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 11:31:06,625 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@b6ae271
2025-07-31 11:31:06,625 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-31 11:31:09,760 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@608cff9e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5de49e5a, org.springframework.security.web.context.SecurityContextPersistenceFilter@55de7b8d, org.springframework.security.web.header.HeaderWriterFilter@6d0acf9a, org.springframework.security.web.authentication.logout.LogoutFilter@246eba0, org.springframework.web.filter.CorsFilter@318f2e9d, com.wzsec.modules.security.security.TokenFilter@4497e084, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3df05259, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@72df13c3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5a4cf76c, org.springframework.security.web.session.SessionManagementFilter@4827711c, org.springframework.security.web.access.ExceptionTranslationFilter@623e8e88, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@729bdd9d]
2025-07-31 11:31:09,812 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-31 11:31:12,936 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 11:31:12,936 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 11:31:12,936 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 11:31:12,936 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 11:31:12,936 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 11:31:12,936 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 11:31:12,936 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 11:31:12,936 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3dcafc9c
2025-07-31 11:31:13,350 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-31 11:31:13,422 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-31 11:31:13,423 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-31 11:31:13,423 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-31 11:31:13,423 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-31 11:31:13,423 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-31 11:31:13,423 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 11:31:13,423 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 11:31:13,423 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:31:13,423 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 11:31:13,423 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:31:13,423 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-31 11:31:13,423 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-31 11:31:13,423 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 11:31:13,437 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 33.287 seconds (JVM running for 35.912)
2025-07-31 11:31:14,241 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-31 11:31:14,241 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-31 14:19:23,020 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 20900 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-31 14:19:23,026 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-31 14:19:25,237 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-31 14:19:25,253 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 14:19:26,156 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 889 ms. Found 88 JPA repository interfaces.
2025-07-31 14:19:26,628 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-31 14:19:26,628 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 14:19:26,630 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 14:19:26,630 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 14:19:26,630 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 14:19:26,630 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-31 14:19:26,630 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-31 14:19:26,630 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 14:19:26,630 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 14:19:27,324 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-31 14:19:27,338 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-31 14:19:27,338 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-31 14:19:28,342 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-31 14:19:28,579 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-31 14:19:28,604 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-31 14:19:28,605 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-31 14:19:28,605 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-31 14:19:28,605 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-31 14:19:28,606 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-31 14:19:28,606 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-31 14:19:28,608 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-31 14:19:28,608 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-31 14:19:32,223 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-31 14:19:32,876 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 14:19:33,054 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-31 14:19:33,526 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 14:19:33,983 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-31 14:19:42,125 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 14:19:42,166 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 14:19:45,930 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-31 14:19:46,318 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 22713 ms
2025-07-31 14:19:50,402 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-31 14:19:51,203 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-31 14:19:51,381 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 14:19:51,471 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 14:19:51,472 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 14:19:51,490 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 14:19:51,490 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 14:19:51,490 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-31 14:19:51,490 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 14:19:51,490 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@1ddf7d19
2025-07-31 14:19:51,490 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-31 14:19:53,766 WARN (AbstractApplicationContext.java:591)- Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'logAspect' defined in file [D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_logging\target\classes\com\wzsec\aspect\LogAspect.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'logServiceImpl' defined in file [D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_logging\target\classes\com\wzsec\service\impl\LogServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'logErrorMapperImpl' defined in file [D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_logging\target\classes\com\wzsec\service\mapper\LogErrorMapperImpl.class]: Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.wzsec.service.mapper.LogErrorMapperImpl]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problems: 
	The hierarchy of the type LogErrorMapperImpl is inconsistent
	The method toEntity(LogErrorDTO) of type LogErrorMapperImpl must override or implement a supertype method
	The method toDto(Log) of type LogErrorMapperImpl must override or implement a supertype method
	The method toEntity(List<LogErrorDTO>) of type LogErrorMapperImpl must override or implement a supertype method
	The method toDto(List<Log>) of type LogErrorMapperImpl must override or implement a supertype method

2025-07-31 14:19:53,777 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 14:19:53,777 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 14:19:53,777 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 14:19:53,792 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 14:19:53,823 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-31 14:19:53,912 INFO (ConditionEvaluationReportLoggingListener.java:136)- 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-31 14:19:54,003 ERROR (SpringApplication.java:870)- Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'logAspect' defined in file [D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_logging\target\classes\com\wzsec\aspect\LogAspect.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'logServiceImpl' defined in file [D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_logging\target\classes\com\wzsec\service\impl\LogServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'logErrorMapperImpl' defined in file [D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_logging\target\classes\com\wzsec\service\mapper\LogErrorMapperImpl.class]: Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.wzsec.service.mapper.LogErrorMapperImpl]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problems: 
	The hierarchy of the type LogErrorMapperImpl is inconsistent
	The method toEntity(LogErrorDTO) of type LogErrorMapperImpl must override or implement a supertype method
	The method toDto(Log) of type LogErrorMapperImpl must override or implement a supertype method
	The method toEntity(List<LogErrorDTO>) of type LogErrorMapperImpl must override or implement a supertype method
	The method toDto(List<Log>) of type LogErrorMapperImpl must override or implement a supertype method

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:920)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:780)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:453)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:343)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1370)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1359)
	at com.wzsec.BDMSEngineRun.main(BDMSEngineRun.java:67)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'logServiceImpl' defined in file [D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_logging\target\classes\com\wzsec\service\impl\LogServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'logErrorMapperImpl' defined in file [D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_logging\target\classes\com\wzsec\service\mapper\LogErrorMapperImpl.class]: Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.wzsec.service.mapper.LogErrorMapperImpl]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problems: 
	The hierarchy of the type LogErrorMapperImpl is inconsistent
	The method toEntity(LogErrorDTO) of type LogErrorMapperImpl must override or implement a supertype method
	The method toDto(Log) of type LogErrorMapperImpl must override or implement a supertype method
	The method toEntity(List<LogErrorDTO>) of type LogErrorMapperImpl must override or implement a supertype method
	The method toDto(List<Log>) of type LogErrorMapperImpl must override or implement a supertype method

	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'logErrorMapperImpl' defined in file [D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_logging\target\classes\com\wzsec\service\mapper\LogErrorMapperImpl.class]: Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.wzsec.service.mapper.LogErrorMapperImpl]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problems: 
	The hierarchy of the type LogErrorMapperImpl is inconsistent
	The method toEntity(LogErrorDTO) of type LogErrorMapperImpl must override or implement a supertype method
	The method toDto(Log) of type LogErrorMapperImpl must override or implement a supertype method
	The method toEntity(List<LogErrorDTO>) of type LogErrorMapperImpl must override or implement a supertype method
	The method toDto(List<Log>) of type LogErrorMapperImpl must override or implement a supertype method

	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 33 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.wzsec.service.mapper.LogErrorMapperImpl]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problems: 
	The hierarchy of the type LogErrorMapperImpl is inconsistent
	The method toEntity(LogErrorDTO) of type LogErrorMapperImpl must override or implement a supertype method
	The method toDto(Log) of type LogErrorMapperImpl must override or implement a supertype method
	The method toEntity(List<LogErrorDTO>) of type LogErrorMapperImpl must override or implement a supertype method
	The method toDto(List<Log>) of type LogErrorMapperImpl must override or implement a supertype method

	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:224)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:87)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1326)
	... 45 common frames omitted
Caused by: java.lang.Error: Unresolved compilation problems: 
	The hierarchy of the type LogErrorMapperImpl is inconsistent
	The method toEntity(LogErrorDTO) of type LogErrorMapperImpl must override or implement a supertype method
	The method toDto(Log) of type LogErrorMapperImpl must override or implement a supertype method
	The method toEntity(List<LogErrorDTO>) of type LogErrorMapperImpl must override or implement a supertype method
	The method toDto(List<Log>) of type LogErrorMapperImpl must override or implement a supertype method

	at com.wzsec.service.mapper.LogErrorMapperImpl.<init>(LogErrorMapperImpl.java:16)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:211)
	... 47 common frames omitted
2025-07-31 14:27:22,743 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 26848 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-31 14:27:22,751 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-31 14:27:24,770 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-31 14:27:24,776 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 14:27:25,618 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 831 ms. Found 88 JPA repository interfaces.
2025-07-31 14:27:26,103 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-31 14:27:26,103 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 14:27:26,108 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 14:27:26,109 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 14:27:26,111 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 14:27:26,112 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-31 14:27:26,114 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-31 14:27:26,114 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 14:27:26,114 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 14:27:26,796 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-31 14:27:26,810 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-31 14:27:26,814 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-31 14:27:27,743 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-31 14:27:27,990 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-31 14:27:28,016 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-31 14:27:28,016 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-31 14:27:28,016 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-31 14:27:28,016 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-31 14:27:28,016 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-31 14:27:28,018 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-31 14:27:28,018 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-31 14:27:28,022 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-31 14:27:30,673 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-31 14:27:31,244 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 14:27:31,439 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-31 14:27:31,920 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 14:27:32,368 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-31 14:27:38,160 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 14:27:38,203 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 14:27:42,062 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-31 14:27:42,442 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 19229 ms
2025-07-31 14:27:46,320 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-31 14:27:47,068 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-31 14:27:47,233 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 14:27:47,330 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 14:27:47,330 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 14:27:47,345 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 14:27:47,345 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 14:27:47,345 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-31 14:27:47,345 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 14:27:47,345 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@702e553b
2025-07-31 14:27:47,345 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-31 14:27:50,302 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@287d5f50, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2ab75fdc, org.springframework.security.web.context.SecurityContextPersistenceFilter@623e8e88, org.springframework.security.web.header.HeaderWriterFilter@59aba9b3, org.springframework.security.web.authentication.logout.LogoutFilter@2773791d, org.springframework.web.filter.CorsFilter@43fbd29, com.wzsec.modules.security.security.TokenFilter@4509f40f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6d0acf9a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@cda805a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@e78bdea, org.springframework.security.web.session.SessionManagementFilter@327a3968, org.springframework.security.web.access.ExceptionTranslationFilter@42ec40dc, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5398adc9]
2025-07-31 14:27:50,339 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-31 14:27:53,345 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 14:27:53,346 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 14:27:53,346 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 14:27:53,346 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 14:27:53,346 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 14:27:53,346 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 14:27:53,346 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 14:27:53,346 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4ac8c723
2025-07-31 14:27:53,727 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-31 14:27:53,800 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-31 14:27:53,805 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-31 14:27:53,805 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-31 14:27:53,805 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-31 14:27:53,805 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-31 14:27:53,805 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 14:27:53,805 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 14:27:53,805 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 14:27:53,805 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 14:27:53,805 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 14:27:53,809 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-31 14:27:53,809 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-31 14:27:53,809 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 14:27:53,820 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 31.744 seconds (JVM running for 34.493)
2025-07-31 14:27:54,518 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-31 14:27:54,518 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-31 14:28:54,502 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 16:58:23,832 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 20788 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-31 16:58:23,839 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-31 16:58:27,480 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-31 16:58:27,485 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 16:58:29,422 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1898 ms. Found 88 JPA repository interfaces.
2025-07-31 16:58:30,042 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-31 16:58:30,043 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 16:58:30,046 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 16:58:30,046 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 16:58:30,050 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 16:58:30,052 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-31 16:58:30,053 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-31 16:58:30,053 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 16:58:30,053 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 16:58:30,991 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-31 16:58:31,015 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-31 16:58:31,020 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-31 16:58:32,361 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-31 16:58:32,644 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-31 16:58:32,675 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-31 16:58:32,676 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-31 16:58:32,676 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-31 16:58:32,677 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-31 16:58:32,677 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-31 16:58:32,677 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-31 16:58:32,681 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-31 16:58:32,684 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-31 16:58:35,667 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-31 16:58:36,307 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 16:58:36,504 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-31 16:58:37,088 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 16:58:37,927 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-31 16:58:45,594 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 16:58:45,645 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 16:58:49,775 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-31 16:58:50,289 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 25907 ms
2025-07-31 16:58:54,954 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-31 16:58:55,725 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-31 16:58:55,901 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 16:58:56,007 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 16:58:56,007 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 16:58:56,029 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 16:58:56,034 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 16:58:56,034 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-31 16:58:56,034 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 16:58:56,034 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@596ecd6c
2025-07-31 16:58:56,034 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-31 16:58:59,250 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7a3953b4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@67196a65, org.springframework.security.web.context.SecurityContextPersistenceFilter@532f5c5d, org.springframework.security.web.header.HeaderWriterFilter@1a68af, org.springframework.security.web.authentication.logout.LogoutFilter@44ebf052, org.springframework.web.filter.CorsFilter@22bd3618, com.wzsec.modules.security.security.TokenFilter@6efa105a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@557415ec, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1b4c8306, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@63730298, org.springframework.security.web.session.SessionManagementFilter@20e3f5b9, org.springframework.security.web.access.ExceptionTranslationFilter@6e0ef532, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1b5a471d]
2025-07-31 16:58:59,295 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-31 16:59:02,506 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 16:59:02,506 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 16:59:02,507 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 16:59:02,507 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 16:59:02,507 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 16:59:02,507 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 16:59:02,507 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 16:59:02,508 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@33176d2e
2025-07-31 16:59:02,889 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-31 16:59:02,962 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-31 16:59:02,964 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-31 16:59:02,964 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-31 16:59:02,964 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-31 16:59:02,964 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-31 16:59:02,964 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 16:59:02,964 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 16:59:02,964 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 16:59:02,964 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 16:59:02,964 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 16:59:02,973 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-31 16:59:02,973 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-31 16:59:02,973 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 16:59:02,986 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 39.8 seconds (JVM running for 42.144)
2025-07-31 16:59:03,730 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-31 16:59:03,730 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-31 17:02:01,735 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-31 17:02:01,738 INFO (FrameworkServlet.java:547)- Completed initialization in 2 ms
2025-07-31 17:03:55,204 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 17:03:55,979 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-31 17:03:55,979 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 17:03:55,979 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 17:03:55,981 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 17:03:56,004 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 17:03:56,006 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 17:03:56,006 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 17:03:56,015 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 17:03:56,058 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-31 17:04:00,900 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 23056 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-31 17:04:00,905 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-31 17:04:03,063 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-31 17:04:03,063 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 17:04:03,936 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 869 ms. Found 88 JPA repository interfaces.
2025-07-31 17:04:04,418 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-31 17:04:04,418 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 17:04:04,418 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 17:04:04,418 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 17:04:04,427 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 17:04:04,429 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-31 17:04:04,429 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-31 17:04:04,430 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 17:04:04,430 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 17:04:05,091 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-31 17:04:05,100 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-31 17:04:05,109 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-31 17:04:06,076 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-31 17:04:06,341 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-31 17:04:06,369 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-31 17:04:06,369 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-31 17:04:06,369 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-31 17:04:06,369 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-31 17:04:06,371 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-31 17:04:06,371 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-31 17:04:06,373 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-31 17:04:06,376 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-31 17:04:09,033 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-31 17:04:09,600 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 17:04:09,819 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-31 17:04:10,343 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 17:04:10,858 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-31 17:04:17,218 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 17:04:17,261 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 17:04:20,992 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-31 17:04:21,363 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 19991 ms
2025-07-31 17:04:25,311 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-31 17:04:26,035 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-31 17:04:26,191 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 17:04:26,283 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 17:04:26,283 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 17:04:26,297 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 17:04:26,312 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 17:04:26,312 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-31 17:04:26,312 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 17:04:26,313 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@2c6dab96
2025-07-31 17:04:26,313 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-31 17:04:29,464 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6f779773, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@45ca5faf, org.springframework.security.web.context.SecurityContextPersistenceFilter@2406cc58, org.springframework.security.web.header.HeaderWriterFilter@4d0f96ac, org.springframework.security.web.authentication.logout.LogoutFilter@2a69158a, org.springframework.web.filter.CorsFilter@44e49229, com.wzsec.modules.security.security.TokenFilter@6f79d583, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@76fc0fa, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@14992832, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@d5fd167, org.springframework.security.web.session.SessionManagementFilter@5b0fad3d, org.springframework.security.web.access.ExceptionTranslationFilter@7ba9104f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@57893f3b]
2025-07-31 17:04:29,506 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-31 17:04:32,555 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 17:04:32,555 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 17:04:32,555 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 17:04:32,555 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 17:04:32,555 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 17:04:32,555 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 17:04:32,555 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 17:04:32,555 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5e2c3e80
2025-07-31 17:04:32,930 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-31 17:04:33,005 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-31 17:04:33,009 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-31 17:04:33,011 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-31 17:04:33,011 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-31 17:04:33,011 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-31 17:04:33,011 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 17:04:33,011 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 17:04:33,011 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 17:04:33,011 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 17:04:33,011 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 17:04:33,011 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-31 17:04:33,011 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-31 17:04:33,011 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 17:04:33,027 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 32.78 seconds (JVM running for 35.176)
2025-07-31 17:04:33,739 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-31 17:04:33,739 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-31 17:06:00,963 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-31 17:06:00,963 INFO (FrameworkServlet.java:547)- Completed initialization in 0 ms
2025-07-31 17:08:21,719 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 17:10:53,435 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 3276 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-31 17:10:53,445 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-31 17:10:56,004 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-31 17:10:56,004 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 17:10:56,930 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 909 ms. Found 88 JPA repository interfaces.
2025-07-31 17:10:57,399 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-31 17:10:57,399 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 17:10:57,416 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 17:10:57,416 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 17:10:57,416 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 17:10:57,416 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-31 17:10:57,416 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-31 17:10:57,416 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 17:10:57,416 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 17:10:58,095 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-31 17:10:58,109 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-31 17:10:58,112 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-31 17:10:59,015 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-31 17:10:59,260 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-31 17:10:59,283 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-31 17:10:59,283 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-31 17:10:59,283 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-31 17:10:59,283 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-31 17:10:59,283 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-31 17:10:59,283 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-31 17:10:59,286 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-31 17:10:59,286 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-31 17:11:01,712 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-31 17:11:02,274 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 17:11:02,468 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-31 17:11:02,916 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 17:11:03,370 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-31 17:11:09,343 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 17:11:09,375 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 17:11:12,996 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-31 17:11:13,350 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 19140 ms
2025-07-31 17:11:17,330 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-31 17:11:18,101 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-31 17:11:18,337 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 17:11:18,461 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 17:11:18,462 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 17:11:18,485 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 17:11:18,487 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 17:11:18,487 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-31 17:11:18,487 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 17:11:18,487 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@490bc86e
2025-07-31 17:11:18,487 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-31 17:11:21,758 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1c1d14d6, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@e8c973, org.springframework.security.web.context.SecurityContextPersistenceFilter@68fbb22, org.springframework.security.web.header.HeaderWriterFilter@6056e9b4, org.springframework.security.web.authentication.logout.LogoutFilter@6a907765, org.springframework.web.filter.CorsFilter@69b74dc2, com.wzsec.modules.security.security.TokenFilter@7314e66f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2d83ea1b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@63730298, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4f770f7e, org.springframework.security.web.session.SessionManagementFilter@2a69158a, org.springframework.security.web.access.ExceptionTranslationFilter@3611cae8, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@11214149]
2025-07-31 17:11:21,801 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-31 17:11:25,103 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 17:11:25,105 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 17:11:25,105 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 17:11:25,105 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 17:11:25,105 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 17:11:25,105 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 17:11:25,106 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 17:11:25,106 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@379327f3
2025-07-31 17:11:25,536 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-31 17:11:25,607 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-31 17:11:25,610 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-31 17:11:25,611 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-31 17:11:25,612 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-31 17:11:25,612 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-31 17:11:25,612 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 17:11:25,612 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 17:11:25,612 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 17:11:25,612 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 17:11:25,613 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 17:11:25,615 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-31 17:11:25,616 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-31 17:11:25,616 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 17:11:25,628 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 33.255 seconds (JVM running for 36.044)
2025-07-31 17:11:26,408 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-31 17:11:26,408 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-31 17:12:01,404 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-31 17:12:01,410 INFO (FrameworkServlet.java:547)- Completed initialization in 0 ms
2025-07-31 17:13:26,522 INFO (DoPictureTaskServiceImpl.java:66)- 开始执行图片脱敏任务
2025-07-31 17:13:38,544 INFO (DoPictureTaskServiceImpl.java:133)- 根据路径特征判断为目录: \data\zhengh\ds_bdms\system\fc
2025-07-31 17:13:38,544 INFO (DoPictureTaskServiceImpl.java:148)- 批处理模式 - inputPath: \data\zhengh\ds_bdms\system\fc, isBatch: true
2025-07-31 17:13:38,553 INFO (DockerRunner.java:74)- DockerRunner 参数 - inputPath: \data\zhengh\ds_bdms\system\fc, isBatch: true, fileName: null
2025-07-31 17:13:38,553 INFO (DockerRunner.java:78)- 准备执行 Docker 命令:
docker run --rm -v /data/zhengh/ds_bdms/system/fc:/app/input -v /data/zhengh/ds_bdms/system/fc2:/app/output mosaic_v1:latest -type image -input /app/input -output /app/output --batch -target yolov8_license_plate -target yolov8n-face --enable-ocr
2025-07-31 17:13:38,583 ERROR (DockerRunner.java:98)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:81)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:151)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-31 17:13:38,585 WARN (DoPictureTaskServiceImpl.java:172)- Docker 处理失败，退出码: -1
2025-07-31 17:13:39,056 WARN (DoPictureTaskServiceImpl.java:438)- 目录不存在: /data/zhengh/ds_bdms/system/fc
2025-07-31 17:13:39,056 INFO (DoPictureTaskServiceImpl.java:225)- Docker执行完成后收集到输入文件数量: 0
2025-07-31 17:13:39,056 WARN (DoPictureTaskServiceImpl.java:438)- 目录不存在: /data/zhengh/ds_bdms/system/fc2
2025-07-31 17:13:39,059 INFO (DoPictureTaskServiceImpl.java:253)- 文件信息收集完成，Docker退出码: -1, 输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-31 17:13:39,853 INFO (DoPictureTaskServiceImpl.java:282)- 执行图片脱敏总时长为: 2秒
2025-07-31 17:13:39,854 INFO (DoPictureTaskServiceImpl.java:283)- 任务ID【32】,执行图片脱敏任务结束
2025-07-31 17:13:39,854 INFO (DoPictureTaskServiceImpl.java:284)- 任务ID【32】,执行完毕
2025-07-31 17:14:14,564 INFO (DoPictureTaskServiceImpl.java:66)- 开始执行图片脱敏任务
2025-07-31 17:14:20,275 INFO (DoPictureTaskServiceImpl.java:133)- 根据路径特征判断为目录: \data\zhengh\ds_bdms\system\fc
2025-07-31 17:14:20,275 INFO (DoPictureTaskServiceImpl.java:148)- 批处理模式 - inputPath: \data\zhengh\ds_bdms\system\fc, isBatch: true
2025-07-31 17:14:20,275 INFO (DockerRunner.java:74)- DockerRunner 参数 - inputPath: \data\zhengh\ds_bdms\system\fc, isBatch: true, fileName: null
2025-07-31 17:14:20,275 INFO (DockerRunner.java:78)- 准备执行 Docker 命令:
docker run --rm -v /data/zhengh/ds_bdms/system/fc:/app/input -v /data/zhengh/ds_bdms/system/fc2:/app/output mosaic_v1:latest -type image -input /app/input -output /app/output --batch -target yolov8_license_plate -target yolov8n-face --enable-ocr
2025-07-31 17:14:20,275 ERROR (DockerRunner.java:98)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:81)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:151)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-31 17:14:20,275 WARN (DoPictureTaskServiceImpl.java:172)- Docker 处理失败，退出码: -1
2025-07-31 17:14:30,358 WARN (DoPictureTaskServiceImpl.java:438)- 目录不存在: /data/zhengh/ds_bdms/system/fc
2025-07-31 17:14:30,358 INFO (DoPictureTaskServiceImpl.java:225)- Docker执行完成后收集到输入文件数量: 0
2025-07-31 17:14:30,358 WARN (DoPictureTaskServiceImpl.java:438)- 目录不存在: /data/zhengh/ds_bdms/system/fc2
2025-07-31 17:14:30,358 INFO (DoPictureTaskServiceImpl.java:253)- 文件信息收集完成，Docker退出码: -1, 输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-31 17:14:30,805 INFO (DoPictureTaskServiceImpl.java:282)- 执行图片脱敏总时长为: 11秒
2025-07-31 17:14:30,805 INFO (DoPictureTaskServiceImpl.java:283)- 任务ID【32】,执行图片脱敏任务结束
2025-07-31 17:14:30,805 INFO (DoPictureTaskServiceImpl.java:284)- 任务ID【32】,执行完毕
2025-07-31 17:14:42,260 INFO (DoPictureTaskServiceImpl.java:66)- 开始执行图片脱敏任务
2025-07-31 17:14:45,300 INFO (DoPictureTaskServiceImpl.java:133)- 根据路径特征判断为目录: \data\zhengh\ds_bdms\system\fc
2025-07-31 17:14:45,300 INFO (DoPictureTaskServiceImpl.java:148)- 批处理模式 - inputPath: \data\zhengh\ds_bdms\system\fc, isBatch: true
2025-07-31 17:14:45,300 INFO (DockerRunner.java:74)- DockerRunner 参数 - inputPath: \data\zhengh\ds_bdms\system\fc, isBatch: true, fileName: null
2025-07-31 17:14:45,300 INFO (DockerRunner.java:78)- 准备执行 Docker 命令:
docker run --rm -v /data/zhengh/ds_bdms/system/fc:/app/input -v /data/zhengh/ds_bdms/system/fc2:/app/output mosaic_v1:latest -type image -input /app/input -output /app/output --batch -target yolov8_license_plate -target yolov8n-face --enable-ocr
2025-07-31 17:14:45,302 ERROR (DockerRunner.java:98)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:81)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:151)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-31 17:14:45,311 WARN (DoPictureTaskServiceImpl.java:172)- Docker 处理失败，退出码: -1
2025-07-31 17:14:51,022 WARN (DoPictureTaskServiceImpl.java:438)- 目录不存在: /data/zhengh/ds_bdms/system/fc
2025-07-31 17:14:51,024 INFO (DoPictureTaskServiceImpl.java:225)- Docker执行完成后收集到输入文件数量: 0
2025-07-31 17:14:51,024 WARN (DoPictureTaskServiceImpl.java:438)- 目录不存在: /data/zhengh/ds_bdms/system/fc2
2025-07-31 17:14:51,024 INFO (DoPictureTaskServiceImpl.java:253)- 文件信息收集完成，Docker退出码: -1, 输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-31 17:14:51,489 INFO (DoPictureTaskServiceImpl.java:282)- 执行图片脱敏总时长为: 6秒
2025-07-31 17:14:51,489 INFO (DoPictureTaskServiceImpl.java:283)- 任务ID【32】,执行图片脱敏任务结束
2025-07-31 17:14:51,489 INFO (DoPictureTaskServiceImpl.java:284)- 任务ID【32】,执行完毕
2025-07-31 17:16:19,210 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 17:16:19,950 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-31 17:16:19,950 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 17:16:19,950 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 17:16:19,950 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 17:16:19,976 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 17:16:19,976 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 17:16:19,976 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 17:16:19,990 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 17:16:20,015 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-31 17:16:29,996 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 19600 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-31 17:16:30,003 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-31 17:16:32,006 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-31 17:16:32,006 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 17:16:32,828 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 806 ms. Found 88 JPA repository interfaces.
2025-07-31 17:16:33,287 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-31 17:16:33,289 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 17:16:33,291 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 17:16:33,291 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 17:16:33,291 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 17:16:33,291 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-31 17:16:33,291 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-31 17:16:33,291 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 17:16:33,291 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 17:16:33,897 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-31 17:16:33,912 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-31 17:16:33,912 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-31 17:16:34,820 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-31 17:16:35,073 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-31 17:16:35,097 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-31 17:16:35,097 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-31 17:16:35,097 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-31 17:16:35,097 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-31 17:16:35,097 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-31 17:16:35,097 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-31 17:16:35,097 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-31 17:16:35,097 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-31 17:16:37,600 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-31 17:16:38,172 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 17:16:38,351 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-31 17:16:38,813 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 17:16:39,262 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-31 17:16:45,050 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 17:16:45,082 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 17:16:48,748 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-31 17:16:49,119 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 18684 ms
2025-07-31 17:16:53,353 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-31 17:16:54,097 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-31 17:16:54,259 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 17:16:54,350 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 17:16:54,350 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 17:16:54,365 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 17:16:54,365 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 17:16:54,365 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-31 17:16:54,365 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 17:16:54,365 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@7c692e24
2025-07-31 17:16:54,365 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-31 17:16:57,405 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4497e084, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5a4cf76c, org.springframework.security.web.context.SecurityContextPersistenceFilter@3c1ddea3, org.springframework.security.web.header.HeaderWriterFilter@189d3670, org.springframework.security.web.authentication.logout.LogoutFilter@246eba0, org.springframework.web.filter.CorsFilter@3c31169f, com.wzsec.modules.security.security.TokenFilter@5d3b93b4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6cd3410f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@143cb21, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2454d007, org.springframework.security.web.session.SessionManagementFilter@5f96ea5, org.springframework.security.web.access.ExceptionTranslationFilter@48cd97f1, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@55fd98e2]
2025-07-31 17:16:57,435 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-31 17:17:00,429 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 17:17:00,429 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 17:17:00,429 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 17:17:00,429 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 17:17:00,429 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 17:17:00,429 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 17:17:00,429 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 17:17:00,429 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1287f07e
2025-07-31 17:17:00,818 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-31 17:17:00,887 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-31 17:17:00,892 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-31 17:17:00,892 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-31 17:17:00,892 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-31 17:17:00,892 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-31 17:17:00,892 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 17:17:00,892 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 17:17:00,892 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 17:17:00,892 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 17:17:00,892 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 17:17:00,896 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-31 17:17:00,896 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-31 17:17:00,896 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 17:17:00,907 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 31.489 seconds (JVM running for 33.61)
2025-07-31 17:17:01,634 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-31 17:17:01,637 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-31 17:18:00,858 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-31 17:18:00,859 INFO (FrameworkServlet.java:547)- Completed initialization in 1 ms
2025-07-31 17:18:03,890 INFO (DoPictureTaskServiceImpl.java:66)- 开始执行图片脱敏任务
2025-07-31 17:18:07,065 INFO (DoPictureTaskServiceImpl.java:133)- 根据路径特征判断为目录: \data\zhengh\ds_bdms\system\fc
2025-07-31 17:18:07,065 INFO (DoPictureTaskServiceImpl.java:148)- 批处理模式 - inputPath: \data\zhengh\ds_bdms\system\fc, isBatch: true
2025-07-31 17:18:07,067 INFO (DockerRunner.java:74)- DockerRunner 参数 - inputPath: \data\zhengh\ds_bdms\system\fc, isBatch: true, fileName: null
2025-07-31 17:18:07,067 INFO (DockerRunner.java:78)- 准备执行 Docker 命令:
docker run --rm -v /data/zhengh/ds_bdms/system/fc:/app/input -v /data/zhengh/ds_bdms/system/fc2:/app/output mosaic_v1:latest -type image -input /app/input -output /app/output --batch -target yolov8_license_plate -target yolov8n-face --enable-ocr
2025-07-31 17:18:07,089 ERROR (DockerRunner.java:98)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:81)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:151)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-31 17:18:07,089 WARN (DoPictureTaskServiceImpl.java:172)- Docker 处理失败，退出码: -1
2025-07-31 17:19:13,906 WARN (DoPictureTaskServiceImpl.java:438)- 目录不存在: /data/zhengh/ds_bdms/system/fc
2025-07-31 17:19:13,906 INFO (DoPictureTaskServiceImpl.java:225)- Docker执行完成后收集到输入文件数量: 0
2025-07-31 17:19:13,906 WARN (DoPictureTaskServiceImpl.java:438)- 目录不存在: /data/zhengh/ds_bdms/system/fc2
2025-07-31 17:19:13,919 INFO (DoPictureTaskServiceImpl.java:253)- 文件信息收集完成，Docker退出码: -1, 输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-31 17:19:14,659 INFO (DoPictureTaskServiceImpl.java:282)- 执行图片脱敏总时长为: 67秒
2025-07-31 17:19:14,660 INFO (DoPictureTaskServiceImpl.java:283)- 任务ID【32】,执行图片脱敏任务结束
2025-07-31 17:19:14,660 INFO (DoPictureTaskServiceImpl.java:284)- 任务ID【32】,执行完毕
2025-07-31 17:20:00,503 INFO (DoPictureTaskServiceImpl.java:66)- 开始执行图片脱敏任务
2025-07-31 17:21:12,274 INFO (DoPictureTaskServiceImpl.java:133)- 根据路径特征判断为目录: \data\zhengh\ds_bdms\system\fc
2025-07-31 17:21:12,274 INFO (DoPictureTaskServiceImpl.java:148)- 批处理模式 - inputPath: \data\zhengh\ds_bdms\system\fc, isBatch: true
2025-07-31 17:21:12,274 INFO (DockerRunner.java:74)- DockerRunner 参数 - inputPath: \data\zhengh\ds_bdms\system\fc, isBatch: true, fileName: null
2025-07-31 17:21:12,275 INFO (DockerRunner.java:78)- 准备执行 Docker 命令:
docker run --rm -v /data/zhengh/ds_bdms/system/fc:/app/input -v /data/zhengh/ds_bdms/system/fc2:/app/output mosaic_v1:latest -type image -input /app/input -output /app/output --batch -target yolov8_license_plate -target yolov8n-face --enable-ocr
2025-07-31 17:21:12,280 ERROR (DockerRunner.java:98)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:81)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:151)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-31 17:21:13,036 WARN (DoPictureTaskServiceImpl.java:172)- Docker 处理失败，退出码: -1
2025-07-31 17:21:13,994 WARN (DoPictureTaskServiceImpl.java:438)- 目录不存在: /data/zhengh/ds_bdms/system/fc
2025-07-31 17:21:13,994 INFO (DoPictureTaskServiceImpl.java:225)- Docker执行完成后收集到输入文件数量: 0
2025-07-31 17:21:13,994 WARN (DoPictureTaskServiceImpl.java:438)- 目录不存在: /data/zhengh/ds_bdms/system/fc2
2025-07-31 17:21:13,994 INFO (DoPictureTaskServiceImpl.java:253)- 文件信息收集完成，Docker退出码: -1, 输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-31 17:21:14,445 INFO (DoPictureTaskServiceImpl.java:282)- 执行图片脱敏总时长为: 55秒
2025-07-31 17:21:14,445 INFO (DoPictureTaskServiceImpl.java:283)- 任务ID【32】,执行图片脱敏任务结束
2025-07-31 17:21:14,445 INFO (DoPictureTaskServiceImpl.java:284)- 任务ID【32】,执行完毕
2025-07-31 17:22:00,497 INFO (DoPictureTaskServiceImpl.java:66)- 开始执行图片脱敏任务
2025-07-31 17:22:24,900 INFO (DoPictureTaskServiceImpl.java:133)- 根据路径特征判断为目录: \data\zhengh\ds_bdms\system\fc
2025-07-31 17:22:24,900 INFO (DoPictureTaskServiceImpl.java:148)- 批处理模式 - inputPath: \data\zhengh\ds_bdms\system\fc, isBatch: true
2025-07-31 17:22:24,900 INFO (DockerRunner.java:74)- DockerRunner 参数 - inputPath: \data\zhengh\ds_bdms\system\fc, isBatch: true, fileName: null
2025-07-31 17:22:24,900 INFO (DockerRunner.java:78)- 准备执行 Docker 命令:
docker run --rm -v /data/zhengh/ds_bdms/system/fc:/app/input -v /data/zhengh/ds_bdms/system/fc2:/app/output mosaic_v1:latest -type image -input /app/input -output /app/output --batch -target yolov8_license_plate -target yolov8n-face --enable-ocr
2025-07-31 17:22:24,900 ERROR (DockerRunner.java:98)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:81)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:151)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-31 17:22:38,041 WARN (DoPictureTaskServiceImpl.java:172)- Docker 处理失败，退出码: -1
2025-07-31 17:22:39,942 WARN (DoPictureTaskServiceImpl.java:438)- 目录不存在: /data/zhengh/ds_bdms/system/fc
2025-07-31 17:22:39,942 INFO (DoPictureTaskServiceImpl.java:225)- Docker执行完成后收集到输入文件数量: 0
2025-07-31 17:22:39,942 WARN (DoPictureTaskServiceImpl.java:438)- 目录不存在: /data/zhengh/ds_bdms/system/fc2
2025-07-31 17:22:39,942 INFO (DoPictureTaskServiceImpl.java:253)- 文件信息收集完成，Docker退出码: -1, 输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-31 17:22:40,429 INFO (DoPictureTaskServiceImpl.java:282)- 执行图片脱敏总时长为: 28秒
2025-07-31 17:22:40,429 INFO (DoPictureTaskServiceImpl.java:283)- 任务ID【32】,执行图片脱敏任务结束
2025-07-31 17:22:40,429 INFO (DoPictureTaskServiceImpl.java:284)- 任务ID【32】,执行完毕
2025-07-31 17:23:28,116 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 17:51:22,956 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 28860 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-31 17:51:22,965 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-31 17:51:26,097 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-31 17:51:26,097 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 17:51:27,526 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1398 ms. Found 88 JPA repository interfaces.
2025-07-31 17:51:28,345 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-31 17:51:28,345 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 17:51:28,349 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 17:51:28,349 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 17:51:28,351 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 17:51:28,355 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-31 17:51:28,355 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-31 17:51:28,355 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 17:51:28,355 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 17:51:29,426 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-31 17:51:29,446 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-31 17:51:29,450 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-31 17:51:30,977 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-31 17:51:31,433 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-31 17:51:31,471 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-31 17:51:31,474 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-31 17:51:31,474 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-31 17:51:31,474 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-31 17:51:31,474 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-31 17:51:31,474 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-31 17:51:31,479 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-31 17:51:31,479 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-31 17:51:35,113 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-31 17:51:35,967 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 17:51:36,257 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-31 17:51:36,972 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 17:51:37,582 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-31 17:51:46,887 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 17:51:46,946 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 17:51:52,153 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-31 17:51:52,609 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 28749 ms
2025-07-31 17:51:58,269 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-31 17:51:59,083 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-31 17:51:59,284 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 17:51:59,387 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 17:51:59,387 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 17:51:59,408 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 17:51:59,411 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 17:51:59,411 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-31 17:51:59,411 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 17:51:59,411 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@2bbdba03
2025-07-31 17:51:59,411 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-31 17:52:02,847 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@623e8e88, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1ec5ade, org.springframework.security.web.context.SecurityContextPersistenceFilter@ad55a06, org.springframework.security.web.header.HeaderWriterFilter@3616678d, org.springframework.security.web.authentication.logout.LogoutFilter@3eec221b, org.springframework.web.filter.CorsFilter@cfef34, com.wzsec.modules.security.security.TokenFilter@6d0acf9a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@48b16f22, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@70dd5a12, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@747db396, org.springframework.security.web.session.SessionManagementFilter@181ae1b4, org.springframework.security.web.access.ExceptionTranslationFilter@358834c3, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1a427dc0]
2025-07-31 17:52:02,891 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-31 17:52:06,575 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 17:52:06,576 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 17:52:06,576 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 17:52:06,576 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 17:52:06,576 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 17:52:06,576 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 17:52:06,576 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 17:52:06,576 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@150658c8
2025-07-31 17:52:06,983 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-31 17:52:07,062 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-31 17:52:07,067 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-31 17:52:07,068 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-31 17:52:07,068 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-31 17:52:07,068 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-31 17:52:07,068 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 17:52:07,068 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 17:52:07,069 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 17:52:07,069 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 17:52:07,069 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 17:52:07,074 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-31 17:52:07,074 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-31 17:52:07,074 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 17:52:07,089 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 45.409 seconds (JVM running for 49.04)
2025-07-31 17:52:07,925 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-31 17:52:07,925 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-31 17:54:01,568 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-31 17:54:01,568 INFO (FrameworkServlet.java:547)- Completed initialization in 0 ms
2025-07-31 17:54:04,780 INFO (DoPictureTaskServiceImpl.java:61)- 开始更新图片任务状态为执行中，任务ID: 32
2025-07-31 17:54:05,360 INFO (DoPictureTaskServiceImpl.java:63)- 图片任务状态更新成功，任务ID: 32
2025-07-31 17:54:05,360 INFO (DoPictureTaskServiceImpl.java:68)- 开始执行图片脱敏任务
2025-07-31 17:54:05,840 INFO (DoPictureTaskServiceImpl.java:133)- 根据路径特征判断为目录: \data\zhengh\ds_bdms\system\fc
2025-07-31 17:54:05,840 INFO (DoPictureTaskServiceImpl.java:148)- 批处理模式 - inputPath: \data\zhengh\ds_bdms\system\fc, isBatch: true
2025-07-31 17:54:05,846 INFO (DockerRunner.java:74)- DockerRunner 参数 - inputPath: \data\zhengh\ds_bdms\system\fc, isBatch: true, fileName: null
2025-07-31 17:54:05,846 INFO (DockerRunner.java:78)- 准备执行 Docker 命令:
docker run --rm -v /data/zhengh/ds_bdms/system/fc:/app/input -v /data/zhengh/ds_bdms/system/fc2:/app/output mosaic_v1:latest -type image -input /app/input -output /app/output --batch -target yolov8_license_plate -target yolov8n-face --enable-ocr
2025-07-31 17:54:05,874 ERROR (DockerRunner.java:98)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:81)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:151)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-31 17:57:35,106 WARN (DoPictureTaskServiceImpl.java:178)- Docker 处理失败，退出码: -1
2025-07-31 17:57:35,270 INFO (DoPictureTaskServiceImpl.java:183)- 图片任务执行失败，状态更新完成，任务ID: 32
2025-07-31 17:57:35,276 WARN (DoPictureTaskServiceImpl.java:449)- 目录不存在: /data/zhengh/ds_bdms/system/fc
2025-07-31 17:57:35,276 INFO (DoPictureTaskServiceImpl.java:236)- Docker执行完成后收集到输入文件数量: 0
2025-07-31 17:57:35,278 WARN (DoPictureTaskServiceImpl.java:449)- 目录不存在: /data/zhengh/ds_bdms/system/fc2
2025-07-31 17:57:35,292 INFO (DoPictureTaskServiceImpl.java:264)- 文件信息收集完成，Docker退出码: -1, 输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-31 17:57:36,006 INFO (DoPictureTaskServiceImpl.java:293)- 执行图片脱敏总时长为: 210秒
2025-07-31 17:57:36,006 INFO (DoPictureTaskServiceImpl.java:294)- 任务ID【32】,执行图片脱敏任务结束
2025-07-31 17:57:36,007 INFO (DoPictureTaskServiceImpl.java:295)- 任务ID【32】,执行完毕
2025-07-31 17:57:37,626 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 17:57:38,502 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-31 17:57:38,502 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 17:57:38,502 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 17:57:38,502 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 17:57:38,524 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 17:57:38,524 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 17:57:38,524 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 17:57:38,549 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 17:57:38,580 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
