package com.wzsec.dotask.mask.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.wzsec.dotask.mask.service.DoPictureTaskService;
import com.wzsec.modules.mask.domain.MaskPictaskconfig;
import com.wzsec.modules.mask.domain.MaskPictaskresult;
import com.wzsec.modules.mask.service.MaskPictaskconfigService;
import com.wzsec.modules.mask.service.MaskPictaskresultService;
import com.wzsec.modules.sdd.sdk.domain.SdkApplyconfig;
import com.wzsec.modules.sdd.sdk.domain.SdkOperationrecord;
import com.wzsec.modules.sdd.sdk.repository.SdkApplyconfigRepository;
import com.wzsec.modules.sdd.sdk.repository.SdkOperationrecordRepository;
import com.wzsec.modules.statistics.domain.MaskTaskresultrecords;
import com.wzsec.modules.statistics.service.MaskTaskresultrecordsService;
import com.wzsec.utils.ConfigurationManager;
import com.wzsec.utils.Const;
import com.wzsec.utils.DateUtil;
import com.wzsec.utils.DockerRunner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.sql.Timestamp;
import java.util.*;

@Slf4j
@Service
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class DoPictureTaskServiceImpl implements DoPictureTaskService {

    private final String imageName = ConfigurationManager.getProperty("pictureVideoMosaicImageName"); //镜像名

    private final MaskPictaskconfigService maskPictaskconfigService;

    private final MaskPictaskresultService maskPictaskresultService;

    private final MaskTaskresultrecordsService maskTaskresultrecordsService;

    private final SdkApplyconfigRepository sdkApplyconfigRepository;

    private final SdkOperationrecordRepository sdkOperationrecordRepository;

    public DoPictureTaskServiceImpl(MaskPictaskconfigService maskPictaskconfigService, MaskPictaskresultService maskPictaskresultService, MaskTaskresultrecordsService maskTaskresultrecordsService, SdkApplyconfigRepository sdkApplyconfigRepository, SdkOperationrecordRepository sdkOperationrecordRepository) {
        this.maskPictaskconfigService = maskPictaskconfigService;
        this.maskPictaskresultService = maskPictaskresultService;
        this.maskTaskresultrecordsService = maskTaskresultrecordsService;
        this.sdkApplyconfigRepository = sdkApplyconfigRepository;
        this.sdkOperationrecordRepository = sdkOperationrecordRepository;
    }


    @Async//异步执行
    @Override
    public void execution(Integer id, String submituser) {
        try {
            // 先更新状态为执行中 - 直接调用service方法避免自调用问题
            log.info("开始更新图片任务状态为执行中，任务ID: {}", id);
            maskPictaskconfigService.updateTaskStatus(id, Const.TASK_EXECUTESTATE_EXECUTING);
            log.info("图片任务状态更新成功，任务ID: {}", id);
        } catch (Exception e) {
            log.error("更新图片任务状态失败，任务ID: {}, 错误: {}", id, e.getMessage(), e);
        }

        log.info("开始执行图片脱敏任务");

        String taskMessage = null;
        // 任务开始时间
        String startTime = DateUtil.getNowTime();
        MaskPictaskresult maskPictaskresult = new MaskPictaskresult();

        MaskPictaskconfig maskPicTaskConfig = maskPictaskconfigService.findMaskPicTaskConfigById(id);
        String taskname = maskPicTaskConfig.getTaskname(); //任务编码
        String inputdirectory = maskPicTaskConfig.getInputdirectory(); //原始图片路径
        String outputdirectory = maskPicTaskConfig.getOutputdirectory(); //脱敏后图片目录

        String imageTotal = ""; // 原图片数
        String imageSuccesses = ""; //脱敏成功图片数量

        SdkOperationrecord sdkOperationrecord = new SdkOperationrecord();
        SdkApplyconfig sdkApplyconfig = null;
        String engine = maskPicTaskConfig.getSparefield1();
        if (StrUtil.isNotBlank(engine)) {
            String[] ipPort = engine.split("-")[1].trim().split(":");
            String ip = ipPort[0];
            String port = ipPort[1];
            sdkApplyconfig = sdkApplyconfigRepository.findInfoBySrcurl(ip + ":" + port);
        }

        File inputFile = new File(inputdirectory);
        String inputDirectory;
        String inputFileName = null;
        int dockerExitCode = -1; // 用于记录Docker执行结果

        try {

            // 改进的文件/目录判断逻辑
            if (inputFile.isFile()) {
                // 文件存在且是文件
                inputDirectory = inputFile.getParent();
                inputFileName = inputFile.getName();
                log.info("检测到存在的文件: {}, 目录: {}, 文件名: {}", inputdirectory, inputDirectory, inputFileName);

                // 处理 getParent() 可能返回 null 的情况
                if (inputDirectory == null) {
                    inputDirectory = inputFile.getAbsoluteFile().getParent();
                    log.warn("getParent() 返回 null，使用 getAbsoluteFile().getParent(): {}", inputDirectory);
                }
            } else if (inputFile.isDirectory()) {
                // 目录存在且是目录
                inputDirectory = inputFile.getPath();
                log.info("检测到存在的目录: {}", inputDirectory);
            } else {
                // 文件/目录不存在，根据路径特征判断
                String path = inputFile.getPath();
                if (hasFileExtension(path)) {
                    // 路径看起来像文件（有扩展名）
                    inputDirectory = inputFile.getParent();
                    inputFileName = inputFile.getName();
                    log.info("根据扩展名判断为文件: {}, 目录: {}, 文件名: {}", inputdirectory, inputDirectory, inputFileName);

                    // 处理 getParent() 可能返回 null 的情况
                    if (inputDirectory == null) {
                        inputDirectory = inputFile.getAbsoluteFile().getParent();
                        log.warn("getParent() 返回 null，使用 getAbsoluteFile().getParent(): {}", inputDirectory);
                    }
                } else {
                    // 路径看起来像目录（无扩展名）
                    inputDirectory = inputFile.getPath();
                    log.info("根据路径特征判断为目录: {}", inputDirectory);
                }
            }

            String inputPath;
            boolean isBatch;

            if (StrUtil.isNotBlank(inputFileName)) {
                // 单文件模式：传递文件所在的目录，而不是完整文件路径
                inputPath = inputDirectory;
                isBatch = false; // 单张图片
                log.info("单文件模式 - inputPath: {}, isBatch: {}, fileName: {}", inputPath, isBatch, inputFileName);
            } else {
                inputPath = inputDirectory;
                isBatch = true;  // 整个目录
                log.info("批处理模式 - inputPath: {}, isBatch: {}", inputPath, isBatch);
            }

            dockerExitCode = DockerRunner.runDocker(
                    imageName,
                    inputPath,
                    outputdirectory,
                    "image",
                    Arrays.asList("yolov8_license_plate", "yolov8n-face"),
                    isBatch,
                    inputFileName  // 传递文件名给DockerRunner
            );

            if (dockerExitCode == 0) {
                log.info("Docker 处理成功");

                // 更新任务表
                try {
                    maskPictaskconfigService.updateTaskStatus(id, Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS);
                    log.info("图片任务执行成功，状态更新完成，任务ID: {}", id);
                } catch (Exception e) {
                    log.error("更新图片任务成功状态失败，任务ID: {}, 错误: {}", id, e.getMessage(), e);
                }

                maskPictaskresult.setTaskstatus(Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS); //任务执行结果状态
                taskMessage = Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS_MESSAGE;

                sdkOperationrecord.setObjectname(maskPicTaskConfig.getTaskname());
                sdkOperationrecord.setOperation("图片脱敏任务执行成功");
            } else {
                log.warn("Docker 处理失败，退出码: {}", dockerExitCode);

                // 更新任务表
                try {
                    maskPictaskconfigService.updateTaskStatus(id, Const.TASK_EXECUTESTATE_EXECUTE_FAIL);
                    log.info("图片任务执行失败，状态更新完成，任务ID: {}", id);
                } catch (Exception e) {
                    log.error("更新图片任务失败状态失败，任务ID: {}, 错误: {}", id, e.getMessage(), e);
                }

                maskPictaskresult.setTaskstatus(Const.TASK_EXECUTESTATE_EXECUTE_FAIL); //任务执行结果状态
                taskMessage = Const.TASK_EXECUTESTATE_EXECUTE_FAIL_MESSAGE;

                sdkOperationrecord.setObjectname(maskPicTaskConfig.getTaskname());
                sdkOperationrecord.setOperation("图片脱敏任务执行失败");
            }

        } catch (Exception e) {
            // 更新任务表
            maskPicTaskConfig.setExecutionstate(Const.TASK_EXECUTESTATE_EXECUTE_FAIL); //执行失败
            maskPictaskconfigService.update(maskPicTaskConfig);
            maskPictaskresult.setTaskstatus(Const.TASK_EXECUTESTATE_EXECUTE_FAIL); //任务执行结果状态
            maskPictaskresult.setBeforepicamount(imageTotal); //原图片数
            maskPictaskresult.setAfterpicamount(imageSuccesses); //脱敏成功图片数量
            taskMessage = Const.TASK_EXECUTESTATE_EXECUTE_FAIL_MESSAGE;
            log.error("执行图片脱敏任务失败", e);
            sdkOperationrecord.setObjectname(maskPicTaskConfig.getTaskname());
            sdkOperationrecord.setOperation("图片脱敏任务执行失败");
        }

        //String str = RuntimeUtil.execForStr("docker run -v /source/:/source/ -v /out/:/out/ -v /etc/localtime:/etc/localtime --rm wzs:vmask_1.05 /wzsvmask/wzsvideomask -s /source/ -d /out/ -m 003,002,001 -v CPU -f .jpg -c 123456");

        // 结束时间
        String endTime = DateUtil.getNowTime();
        // 执行总时间
        String totalTime = String.valueOf(DateUtil.getTimeSecondsByBothDate(startTime, endTime));

        //脱敏结果
        maskPictaskresult.setTaskname(taskname);//任务名
        maskPictaskresult.setInputdirectory(inputdirectory); //原始图片路径
        //maskPictaskresult.setBeforepicamount(imageTotal); //原图片数
        //maskPictaskresult.setMaskobject(maskobject); //脱敏对象
        maskPictaskresult.setOutputdirectory(outputdirectory); //脱敏后图片目录
        //maskPictaskresult.setOutputfileformat(outputfileformat); //输出文件格式
        maskPictaskresult.setAfterpicamount(imageSuccesses); //脱敏成功图片数量
        maskPictaskresult.setJobtotaltime(totalTime); //任务耗时
        maskPictaskresult.setJobendtime(endTime); //任务结束时间

        // 在图片马赛克完成后写入结果表之前对脱敏前路径下文件及脱敏后文件进行相关信息(文件夹下文件名,文件大小)提取,并展示在结果表中,以json格式存储到sparefield1,方便前端读取
        //inputDirectory 输入目录,outputdirectory 输出目录 ,仅记录图片格式文件,支持的图片扩展名: .jpg', .jpeg, .png, .bmp

        // 在Docker执行完成后进行文件信息收集和统计
        try {
            // 收集输入和输出文件信息，建立一一对应关系
            Map<String, Object> fileInfoMap = new HashMap<>();

            // 收集输入文件信息
            List<Map<String, Object>> inputFiles = collectImageFileInfo(inputdirectory);
            log.info("Docker执行完成后收集到输入文件数量: {}", inputFiles.size());

            // 无论Docker成功还是失败，都建立输入输出文件的对应关系
            List<Map<String, Object>> fileMatchResults = matchInputOutputFiles(inputFiles, outputdirectory);

            fileInfoMap.put("fileMatchResults", fileMatchResults);
            fileInfoMap.put("inputDirectory", inputdirectory);
            fileInfoMap.put("outputDirectory", outputdirectory);
            fileInfoMap.put("totalInputFiles", inputFiles.size());

            // 统计处理结果
            long successCount = fileMatchResults.stream().mapToLong(result ->
                    result.get("outputFile") != null ? 1 : 0).sum();
            long failCount = fileMatchResults.size() - successCount;

            fileInfoMap.put("successCount", successCount);
            fileInfoMap.put("failCount", failCount);
            fileInfoMap.put("dockerExitCode", dockerExitCode);

            // 如果Docker执行失败，添加失败标记
            if (dockerExitCode != 0) {
                fileInfoMap.put("dockerExecutionFailed", true);
            }

            // 转换为JSON字符串并存储到sparefield1
            String fileInfoJson = JSON.toJSONString(fileInfoMap);
            maskPictaskresult.setSparefield1(fileInfoJson);

            log.info("文件信息收集完成，Docker退出码: {}, 输入文件数量: {}, 成功处理: {}, 处理失败: {}",
                    dockerExitCode, inputFiles.size(), successCount, failCount);

        } catch (Exception e) {
            log.error("收集文件信息时发生错误: ", e);
        }

        maskPictaskresultService.create(maskPictaskresult);

        //插入任务结果记录表
        MaskTaskresultrecords maskTaskresultrecords = new MaskTaskresultrecords();
        maskTaskresultrecords.setTaskname(maskPictaskresult.getTaskname());
        maskTaskresultrecords.setTasktype(Const.MASK_TASK_PIC);
        maskTaskresultrecords.setTaskstatus(taskMessage);
        maskTaskresultrecords.setStarttime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", startTime));
        maskTaskresultrecords.setEndtime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", endTime));
        maskTaskresultrecordsService.create(maskTaskresultrecords);

        //插入SDK操作记录 20250723 空指针报错排除
        if (sdkApplyconfig != null) {
            sdkOperationrecord.setSdkid(sdkApplyconfig.getSdkid());
            sdkOperationrecord.setSdkname(sdkApplyconfig.getSdkname());
            sdkOperationrecord.setVersion(sdkApplyconfig.getVersion());
            sdkOperationrecord.setApplysystemname(sdkApplyconfig.getApplysystemname());
            sdkOperationrecord.setObjecttype(Const.SDK_OPERATION_PIC);
            sdkOperationrecord.setOperationtime(Timestamp.valueOf(cn.hutool.core.date.DateUtil.now()));
            sdkOperationrecordRepository.save(sdkOperationrecord);
        }

        log.info("执行图片脱敏总时长为: {}秒", totalTime);
        log.info("任务ID【" + id + "】,执行图片脱敏任务结束");
        log.info("任务ID【" + id + "】,执行完毕");
    }

    /**
     * 建立输入文件和输出文件的对应关系（图片处理时优先匹配相同扩展名，再匹配其他扩展名）
     *
     * @param inputFiles 输入文件信息列表
     * @param outputDirectory 输出目录路径
     * @return 文件对应关系列表
     */
    private List<Map<String, Object>> matchInputOutputFiles(List<Map<String, Object>> inputFiles, String outputDirectory) {
        List<Map<String, Object>> matchResults = new ArrayList<>();

        // 收集输出目录下的所有文件信息，建立文件名（不含扩展名）到文件信息列表的映射
        Map<String, List<Map<String, Object>>> outputFileMap = new HashMap<>();
        List<Map<String, Object>> outputFiles = collectImageFileInfo(outputDirectory);

        for (Map<String, Object> outputFile : outputFiles) {
            String fullPath = (String) outputFile.get("fileName");
            String simpleFileName = new File(fullPath).getName();
            String fileNameWithoutExtension = getFileNameWithoutExtension(simpleFileName);

            outputFileMap.computeIfAbsent(fileNameWithoutExtension, k -> new ArrayList<>()).add(outputFile);
        }

        // 为每个输入文件查找对应的输出文件
        for (Map<String, Object> inputFile : inputFiles) {
            Map<String, Object> matchResult = new HashMap<>();
            String inputFullPath = (String) inputFile.get("fileName");
            String inputSimpleFileName = new File(inputFullPath).getName();
            String inputFileNameWithoutExtension = getFileNameWithoutExtension(inputSimpleFileName);
            String inputExtension = getFileExtension(inputSimpleFileName);

            // 设置输入文件信息
            matchResult.put("inputFile", inputFile);

            // 查找对应的输出文件
            List<Map<String, Object>> candidateOutputFiles = outputFileMap.get(inputFileNameWithoutExtension);
            Map<String, Object> bestOutputFile = null;

            if (candidateOutputFiles != null && !candidateOutputFiles.isEmpty()) {
                // 优先查找相同扩展名的文件
                for (Map<String, Object> outputFile : candidateOutputFiles) {
                    String outputFullPath = (String) outputFile.get("fileName");
                    String outputSimpleFileName = new File(outputFullPath).getName();
                    String outputExtension = getFileExtension(outputSimpleFileName);

                    if (inputExtension.equalsIgnoreCase(outputExtension)) {
                        bestOutputFile = outputFile;
                        log.debug("图片文件匹配成功（相同扩展名）: {} -> {}", inputSimpleFileName, outputSimpleFileName);
                        break;
                    }
                }

                // 如果没找到相同扩展名的，选择第一个匹配的文件名
                if (bestOutputFile == null) {
                    bestOutputFile = candidateOutputFiles.get(0);
                    String outputFullPath = (String) bestOutputFile.get("fileName");
                    String outputSimpleFileName = new File(outputFullPath).getName();
                    log.debug("图片文件匹配成功（不同扩展名）: {} -> {}", inputSimpleFileName, outputSimpleFileName);
                }
            }

            if (bestOutputFile != null) {
                matchResult.put("outputFile", bestOutputFile);
                matchResult.put("processed", true);
                matchResult.put("status", "SUCCESS");
            } else {
                matchResult.put("outputFile", null);
                matchResult.put("processed", false);
                matchResult.put("status", "FAILED");
                log.debug("图片文件匹配失败: {} (未找到对应的输出文件)", inputSimpleFileName);
            }

            matchResults.add(matchResult);
        }

        return matchResults;
    }

    /**
     * 获取文件名（不包含扩展名）
     *
     * @param fileName 完整文件名
     * @return 不包含扩展名的文件名
     */
    private String getFileNameWithoutExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return fileName;
        }

        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            // 没有扩展名
            return fileName;
        }

        return fileName.substring(0, lastDotIndex);
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName 完整文件名
     * @return 文件扩展名（不包含点号）
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }

        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            // 没有扩展名或点号在最后
            return "";
        }

        return fileName.substring(lastDotIndex + 1);
    }

    /**
     * 判断路径是否有文件扩展名
     *
     * @param path 文件路径
     * @return true 如果路径看起来像文件
     */
    private boolean hasFileExtension(String path) {
        if (path == null || path.isEmpty()) {
            return false;
        }

        // 获取最后一个点的位置
        int lastDotIndex = path.lastIndexOf('.');
        int lastSeparatorIndex = Math.max(path.lastIndexOf('/'), path.lastIndexOf('\\'));

        // 如果点在最后一个路径分隔符之后，且不是路径的最后一个字符，则认为有扩展名
        return lastDotIndex > lastSeparatorIndex && lastDotIndex < path.length() - 1;
    }

    /**
     * 收集指定目录下的图片文件信息
     *
     * @param directoryPath 目录路径
     * @return 图片文件信息列表
     */
    private List<Map<String, Object>> collectImageFileInfo(String directoryPath) {
        List<Map<String, Object>> fileInfoList = new ArrayList<>();

        if (StrUtil.isBlank(directoryPath)) {
            return fileInfoList;
        }

        File directory = new File(directoryPath);
        if (!directory.exists()) {
            log.warn("目录不存在: {}", directoryPath);
            return fileInfoList;
        }

        // 支持的图片扩展名
        Set<String> supportedExtensions = new HashSet<>(Arrays.asList(
                "jpg", "jpeg", "png", "bmp"
        ));

        try {
            if (directory.isFile()) {
                // 如果是单个文件，检查是否为图片文件
                if (isImageFile(directory, supportedExtensions)) {
                    Map<String, Object> fileInfo = createFileInfoMap(directory);
                    fileInfoList.add(fileInfo);
                }
            } else if (directory.isDirectory()) {
                // 如果是目录，递归收集所有图片文件
                collectImageFilesRecursively(directory, supportedExtensions, fileInfoList);
            }
        } catch (Exception e) {
            log.error("收集目录 {} 下的文件信息时发生错误: ", directoryPath, e);
        }

        return fileInfoList;
    }

    /**
     * 递归收集目录下的图片文件
     *
     * @param directory 目录
     * @param supportedExtensions 支持的扩展名
     * @param fileInfoList 文件信息列表
     */
    private void collectImageFilesRecursively(File directory, Set<String> supportedExtensions, List<Map<String, Object>> fileInfoList) {
        File[] files = directory.listFiles();
        if (files == null) {
            return;
        }

        for (File file : files) {
            if (file.isFile() && isImageFile(file, supportedExtensions)) {
                Map<String, Object> fileInfo = createFileInfoMap(file);
                fileInfoList.add(fileInfo);
            } else if (file.isDirectory()) {
                // 递归处理子目录
                collectImageFilesRecursively(file, supportedExtensions, fileInfoList);
            }
        }
    }

    /**
     * 判断文件是否为图片文件
     *
     * @param file 文件
     * @param supportedExtensions 支持的扩展名
     * @return 是否为图片文件
     */
    private boolean isImageFile(File file, Set<String> supportedExtensions) {
        String fileName = file.getName().toLowerCase();
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return false;
        }
        String extension = fileName.substring(lastDotIndex + 1);
        return supportedExtensions.contains(extension);
    }

    /**
     * 创建文件信息映射
     *
     * @param file 文件
     * @return 文件信息映射
     */
    private Map<String, Object> createFileInfoMap(File file) {
        Map<String, Object> fileInfo = new HashMap<>();
        // 文件名使用完整路径
        fileInfo.put("fileName", file.getAbsolutePath());
        // 文件大小（字节）
        fileInfo.put("fileSize", file.length());
        // 文件大小（格式化）
        fileInfo.put("fileSizeFormatted", formatFileSize(file.length()));
        // 最后修改时间
        fileInfo.put("lastModified", cn.hutool.core.date.DateUtil.formatDateTime(new Date(file.lastModified())));

        return fileInfo;
    }

    /**
     * 格式化文件大小
     *
     * @param size 文件大小（字节）
     * @return 格式化后的文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }

}
