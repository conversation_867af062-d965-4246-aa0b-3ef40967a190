package com.wzsec.modules.sdd.api.service.mapper;

import com.wzsec.modules.sdd.api.domain.ApiRule;
import com.wzsec.modules.sdd.api.service.dto.ApiRuleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T18:00:04+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiRuleMapperImpl implements ApiRuleMapper {

    @Override
    public ApiRule toEntity(ApiRuleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiRule apiRule = new ApiRule();

        apiRule.setId( dto.getId() );
        apiRule.setSname( dto.getSname() );
        apiRule.setRulename( dto.getRulename() );
        apiRule.setAlgorithm( dto.getAlgorithm() );
        apiRule.setParam( dto.getParam() );
        apiRule.setStatus( dto.getStatus() );
        apiRule.setNote( dto.getNote() );
        apiRule.setCreateuser( dto.getCreateuser() );
        apiRule.setCreatetime( dto.getCreatetime() );
        apiRule.setUpdateuser( dto.getUpdateuser() );
        apiRule.setUpdatetime( dto.getUpdatetime() );
        apiRule.setSparefield1( dto.getSparefield1() );
        apiRule.setSparefield2( dto.getSparefield2() );
        apiRule.setSparefield3( dto.getSparefield3() );
        apiRule.setSparefield4( dto.getSparefield4() );
        apiRule.setSparefield5( dto.getSparefield5() );

        return apiRule;
    }

    @Override
    public ApiRuleDto toDto(ApiRule entity) {
        if ( entity == null ) {
            return null;
        }

        ApiRuleDto apiRuleDto = new ApiRuleDto();

        apiRuleDto.setId( entity.getId() );
        apiRuleDto.setSname( entity.getSname() );
        apiRuleDto.setRulename( entity.getRulename() );
        apiRuleDto.setAlgorithm( entity.getAlgorithm() );
        apiRuleDto.setParam( entity.getParam() );
        apiRuleDto.setStatus( entity.getStatus() );
        apiRuleDto.setNote( entity.getNote() );
        apiRuleDto.setCreateuser( entity.getCreateuser() );
        apiRuleDto.setCreatetime( entity.getCreatetime() );
        apiRuleDto.setUpdateuser( entity.getUpdateuser() );
        apiRuleDto.setUpdatetime( entity.getUpdatetime() );
        apiRuleDto.setSparefield1( entity.getSparefield1() );
        apiRuleDto.setSparefield2( entity.getSparefield2() );
        apiRuleDto.setSparefield3( entity.getSparefield3() );
        apiRuleDto.setSparefield4( entity.getSparefield4() );
        apiRuleDto.setSparefield5( entity.getSparefield5() );

        return apiRuleDto;
    }

    @Override
    public List<ApiRule> toEntity(List<ApiRuleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiRule> list = new ArrayList<ApiRule>( dtoList.size() );
        for ( ApiRuleDto apiRuleDto : dtoList ) {
            list.add( toEntity( apiRuleDto ) );
        }

        return list;
    }

    @Override
    public List<ApiRuleDto> toDto(List<ApiRule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiRuleDto> list = new ArrayList<ApiRuleDto>( entityList.size() );
        for ( ApiRule apiRule : entityList ) {
            list.add( toDto( apiRule ) );
        }

        return list;
    }
}
